import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum ThemeMode { light, dark, system }

enum AppTheme {
  green,    // الأخضر (الافتراضي)
  blue,     // الأزرق
  purple,   // البنفسجي
  orange,   // البرتقالي
  teal,     // الأزرق المخضر
  brown,    // البني
}

class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  AppTheme _appTheme = AppTheme.green;
  double _fontSize = 16.0;
  String _fontFamily = 'Amiri';
  bool _useSystemFont = false;
  bool _highContrast = false;
  bool _reducedAnimations = false;

  // Debounce timer for saving
  Timer? _saveTimer;

  // Getters
  ThemeMode get themeMode => _themeMode;
  AppTheme get appTheme => _appTheme;
  double get fontSize => _fontSize;
  String get fontFamily => _fontFamily;
  bool get useSystemFont => _useSystemFont;
  bool get highContrast => _highContrast;
  bool get reducedAnimations => _reducedAnimations;

  // Theme colors map
  static const Map<AppTheme, Color> themeColors = {
    AppTheme.green: Color(0xFF4CAF50),
    AppTheme.blue: Color(0xFF2196F3),
    AppTheme.purple: Color(0xFF9C27B0),
    AppTheme.orange: Color(0xFFFF9800),
    AppTheme.teal: Color(0xFF009688),
    AppTheme.brown: Color(0xFF795548),
  };

  // Get current theme color
  Color get primaryColor => themeColors[_appTheme]!;

  // Get light theme
  ThemeData get lightTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.light,
    );

    return _buildTheme(colorScheme);
  }

  // Get dark theme
  ThemeData get darkTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryColor,
      brightness: Brightness.dark,
    );

    return _buildTheme(colorScheme);
  }

  // Build theme with common settings
  ThemeData _buildTheme(ColorScheme colorScheme) {
    return ThemeData(
      colorScheme: colorScheme,
      useMaterial3: true,
      fontFamily: _useSystemFont ? null : _fontFamily,

      // Text theme
      textTheme: _buildTextTheme(colorScheme),

      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: _fontSize + 4,
          fontWeight: FontWeight.bold,
          fontFamily: _useSystemFont ? null : _fontFamily,
          color: colorScheme.onPrimary,
        ),
      ),

      // Card theme
      cardTheme: CardTheme(
        elevation: _highContrast ? 8 : 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: _highContrast
            ? BorderSide(color: colorScheme.outline, width: 1)
            : BorderSide.none,
        ),
      ),

      // Elevated button theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          textStyle: TextStyle(
            fontSize: _fontSize,
            fontFamily: _useSystemFont ? null : _fontFamily,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // Animation duration
      pageTransitionsTheme: PageTransitionsTheme(
        builders: _reducedAnimations
          ? {
              TargetPlatform.android: const FadeUpwardsPageTransitionsBuilder(),
              TargetPlatform.iOS: const CupertinoPageTransitionsBuilder(),
            }
          : const {
              TargetPlatform.android: ZoomPageTransitionsBuilder(),
              TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
            },
      ),
    );
  }

  // Build text theme
  TextTheme _buildTextTheme(ColorScheme colorScheme) {
    final baseStyle = TextStyle(
      fontFamily: _useSystemFont ? null : _fontFamily,
      color: colorScheme.onSurface,
    );

    return TextTheme(
      displayLarge: baseStyle.copyWith(fontSize: _fontSize + 16),
      displayMedium: baseStyle.copyWith(fontSize: _fontSize + 12),
      displaySmall: baseStyle.copyWith(fontSize: _fontSize + 8),
      headlineLarge: baseStyle.copyWith(fontSize: _fontSize + 8),
      headlineMedium: baseStyle.copyWith(fontSize: _fontSize + 6),
      headlineSmall: baseStyle.copyWith(fontSize: _fontSize + 4),
      titleLarge: baseStyle.copyWith(fontSize: _fontSize + 4),
      titleMedium: baseStyle.copyWith(fontSize: _fontSize + 2),
      titleSmall: baseStyle.copyWith(fontSize: _fontSize),
      bodyLarge: baseStyle.copyWith(fontSize: _fontSize),
      bodyMedium: baseStyle.copyWith(fontSize: _fontSize - 2),
      bodySmall: baseStyle.copyWith(fontSize: _fontSize - 4),
      labelLarge: baseStyle.copyWith(fontSize: _fontSize),
      labelMedium: baseStyle.copyWith(fontSize: _fontSize - 2),
      labelSmall: baseStyle.copyWith(fontSize: _fontSize - 4),
    );
  }

  // Initialize
  Future<void> initialize() async {
    await _loadSettings();
    notifyListeners(); // إضافة هذا السطر لضمان تحديث الواجهة
  }

  // Theme mode methods
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      await _saveSettings();
      notifyListeners();
    }
  }

  // App theme methods
  Future<void> setAppTheme(AppTheme theme) async {
    if (_appTheme != theme) {
      _appTheme = theme;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Font size methods
  Future<void> setFontSize(double size) async {
    if (size >= 12.0 && size <= 24.0 && _fontSize != size) {
      _fontSize = size;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> increaseFontSize() async {
    if (_fontSize < 24.0) {
      _fontSize += 2.0;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> decreaseFontSize() async {
    if (_fontSize > 12.0) {
      _fontSize -= 2.0;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Font family methods
  Future<void> setFontFamily(String family) async {
    if (_fontFamily != family) {
      _fontFamily = family;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> setUseSystemFont(bool use) async {
    if (_useSystemFont != use) {
      _useSystemFont = use;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Accessibility methods
  Future<void> setHighContrast(bool enabled) async {
    if (_highContrast != enabled) {
      _highContrast = enabled;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> setReducedAnimations(bool enabled) async {
    if (_reducedAnimations != enabled) {
      _reducedAnimations = enabled;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Reset to defaults
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _appTheme = AppTheme.green;
    _fontSize = 16.0;
    _fontFamily = 'Amiri';
    _useSystemFont = false;
    _highContrast = false;
    _reducedAnimations = false;

    await _saveSettings();
    notifyListeners();
  }

  // Private methods
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final themeModeIndex = prefs.getInt('themeMode') ?? ThemeMode.system.index;
      if (themeModeIndex >= 0 && themeModeIndex < ThemeMode.values.length) {
        _themeMode = ThemeMode.values[themeModeIndex];
      }

      final appThemeIndex = prefs.getInt('appTheme') ?? AppTheme.green.index;
      if (appThemeIndex >= 0 && appThemeIndex < AppTheme.values.length) {
        _appTheme = AppTheme.values[appThemeIndex];
      }

      _fontSize = prefs.getDouble('fontSize') ?? 16.0;
      _fontFamily = prefs.getString('fontFamily') ?? 'Amiri';
      _useSystemFont = prefs.getBool('useSystemFont') ?? false;
      _highContrast = prefs.getBool('highContrast') ?? false;
      _reducedAnimations = prefs.getBool('reducedAnimations') ?? false;

      debugPrint('تم تحميل إعدادات المظهر: وضع الثيم = ${_themeMode.name}');
    } catch (e) {
      debugPrint('فشل في تحميل إعدادات المظهر: $e');
      // استخدام القيم الافتراضية في حالة الخطأ
      _themeMode = ThemeMode.system;
      _appTheme = AppTheme.green;
      _fontSize = 16.0;
      _fontFamily = 'Amiri';
      _useSystemFont = false;
      _highContrast = false;
      _reducedAnimations = false;
    }
  }

  Future<void> _saveSettings() async {
    // Cancel previous timer
    _saveTimer?.cancel();

    // Set new timer to debounce saves
    _saveTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        final prefs = await SharedPreferences.getInstance();

        await Future.wait([
          prefs.setInt('themeMode', _themeMode.index),
          prefs.setInt('appTheme', _appTheme.index),
          prefs.setDouble('fontSize', _fontSize),
          prefs.setString('fontFamily', _fontFamily),
          prefs.setBool('useSystemFont', _useSystemFont),
          prefs.setBool('highContrast', _highContrast),
          prefs.setBool('reducedAnimations', _reducedAnimations),
        ]);

        debugPrint('تم حفظ إعدادات المظهر بنجاح');
      } catch (e) {
        debugPrint('فشل في حفظ إعدادات المظهر: $e');
      }
    });
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    super.dispose();
  }
}
