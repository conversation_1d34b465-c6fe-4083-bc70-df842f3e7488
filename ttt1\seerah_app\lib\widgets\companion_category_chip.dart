import 'package:flutter/material.dart';

class CompanionCategoryChip extends StatefulWidget {
  final String label;
  final bool isSelected;
  final ValueChanged<bool>? onSelected;

  const CompanionCategoryChip({
    super.key,
    required this.label,
    required this.isSelected,
    this.onSelected,
  });

  @override
  State<CompanionCategoryChip> createState() => _CompanionCategoryChipState();
}

class _CompanionCategoryChipState extends State<CompanionCategoryChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    if (widget.isSelected) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(CompanionCategoryChip oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getCategoryColor() {
    switch (widget.label) {
      case 'العشرة المبشرون بالجنة':
        return const Color(0xFF4CAF50);
      case 'الخلفاء الراشدون':
        return const Color(0xFF2196F3);
      case 'أمهات المؤمنين':
        return const Color(0xFFE91E63);
      case 'الأنصار':
        return const Color(0xFFFF9800);
      case 'المهاجرون':
        return const Color(0xFF9C27B0);
      case 'بدريون':
        return const Color(0xFFF44336);
      case 'أهل بيت النبي':
        return const Color(0xFF607D8B);
      case 'الكل':
        return const Color(0xFF795548);
      default:
        return const Color(0xFF9E9E9E);
    }
  }

  String _getCategoryEmoji() {
    switch (widget.label) {
      case 'العشرة المبشرون بالجنة':
        return '🌟';
      case 'الخلفاء الراشدون':
        return '👑';
      case 'أمهات المؤمنين':
        return '🌹';
      case 'الأنصار':
        return '🏠';
      case 'المهاجرون':
        return '🚶';
      case 'بدريون':
        return '⚔️';
      case 'أهل بيت النبي':
        return '🏡';
      case 'الكل':
        return '👥';
      default:
        return '👤';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: () {
              if (widget.onSelected != null) {
                widget.onSelected!(!widget.isSelected);
              }
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                gradient: widget.isSelected
                    ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          _getCategoryColor(),
                          _getCategoryColor().withValues(alpha: 0.8),
                        ],
                      )
                    : null,
                color: widget.isSelected ? null : Colors.white,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: widget.isSelected
                      ? _getCategoryColor()
                      : Colors.grey[300]!,
                  width: widget.isSelected ? 2 : 1,
                ),
                boxShadow: widget.isSelected
                    ? [
                        BoxShadow(
                          color: _getCategoryColor().withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 1),
                        ),
                      ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getCategoryEmoji(),
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _getShortLabel(),
                    style: TextStyle(
                      color: widget.isSelected ? Colors.white : Colors.grey[700],
                      fontSize: 12,
                      fontWeight: widget.isSelected
                          ? FontWeight.bold
                          : FontWeight.w500,
                      fontFamily: 'Amiri',
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _getShortLabel() {
    switch (widget.label) {
      case 'العشرة المبشرون بالجنة':
        return 'العشرة المبشرون';
      case 'الخلفاء الراشدون':
        return 'الخلفاء';
      case 'أمهات المؤمنين':
        return 'أمهات المؤمنين';
      case 'الأنصار':
        return 'الأنصار';
      case 'المهاجرون':
        return 'المهاجرون';
      case 'بدريون':
        return 'بدريون';
      case 'أهل بيت النبي':
        return 'أهل البيت';
      case 'الكل':
        return 'الكل';
      default:
        return widget.label;
    }
  }
}
