import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/seerah_event.dart';
import '../providers/favorites_provider.dart';

class SeerahEventCard extends StatefulWidget {
  final SeerahEvent event;
  final VoidCallback? onTap;
  final bool showFullDescription;

  const SeerahEventCard({
    super.key,
    required this.event,
    this.onTap,
    this.showFullDescription = false,
  });

  @override
  State<SeerahEventCard> createState() => _SeerahEventCardState();
}

class _SeerahEventCardState extends State<SeerahEventCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getCategoryColor() {
    switch (widget.event.category) {
      case 'ما قبل البعثة':
        return const Color(0xFF8BC34A);
      case 'بداية الوحي والدعوة السرية':
        return const Color(0xFF2196F3);
      case 'الدعوة الجهرية والهجرة':
        return const Color(0xFFFF9800);
      case 'العهد المدني والغزوات':
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF9C27B0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
        if (widget.onTap != null) {
          Future.delayed(const Duration(milliseconds: 100), widget.onTap!);
        }
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: _getCategoryColor().withValues(alpha: 0.2),
                    blurRadius: _isPressed ? 8 : 12,
                    offset: Offset(0, _isPressed ? 2 : 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(),
                    _buildContent(),
                    _buildFooter(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor(),
            _getCategoryColor().withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${widget.event.order}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              Consumer<FavoritesProvider>(
                builder: (context, favoritesProvider, child) {
                  final isFavorite = favoritesProvider.isFavorite(
                    widget.event.id,
                    FavoriteType.seerah,
                  );
                  return IconButton(
                    onPressed: () {
                      favoritesProvider.toggleFavorite(
                        widget.event.id,
                        FavoriteType.seerah,
                      );
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            isFavorite
                                ? 'تم إزالة الحدث من المفضلة'
                                : 'تم إضافة الحدث للمفضلة',
                            style: const TextStyle(fontFamily: 'Amiri'),
                          ),
                          backgroundColor: isFavorite ? Colors.orange : Colors.green,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    icon: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: Colors.white,
                      size: 20,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  );
                },
              ),
              const SizedBox(width: 8),
              Text(
                widget.event.emoji,
                style: const TextStyle(fontSize: 24),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            widget.event.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.event.period,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.showFullDescription
                ? widget.event.description
                : _truncateText(widget.event.description, 100),
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color,
              height: 1.5,
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.location_on, widget.event.location),
          const SizedBox(height: 8),
          _buildInfoRow(Icons.calendar_today, widget.event.christianYear),
          const SizedBox(height: 8),
          _buildInfoRow(Icons.person, widget.event.ageOfProphet),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: _getCategoryColor(),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontFamily: 'Amiri',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).colorScheme.surface.withValues(alpha: 0.5)
            : Colors.grey[50],
        border: Border(
          top: BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)
                : Colors.grey[200]!,
          ),
        ),
      ),
      child: Row(
        children: [
          _buildImportanceStars(),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getCategoryColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              widget.event.category,
              style: TextStyle(
                color: _getCategoryColor(),
                fontSize: 10,
                fontWeight: FontWeight.w500,
                fontFamily: 'Amiri',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImportanceStars() {
    return Row(
      children: List.generate(5, (index) {
        return Icon(
          index < widget.event.importance ? Icons.star : Icons.star_border,
          size: 16,
          color: index < widget.event.importance
              ? Colors.amber
              : Colors.grey[400],
        );
      }),
    );
  }

  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }
}
