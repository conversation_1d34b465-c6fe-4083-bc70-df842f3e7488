﻿import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'screens/seerah_events_screen.dart';
import 'screens/hadith_list_screen.dart';
import 'screens/companions_screen.dart';
import 'screens/advanced_search_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/favorites_screen.dart';
import 'screens/auth_screen.dart';
import 'providers/app_provider.dart';
import 'providers/companions_provider.dart';
import 'providers/theme_provider.dart' as theme_provider;
import 'providers/search_provider.dart';
import 'providers/favorites_provider.dart';
import 'core/error_handler.dart';
import 'services/permission_service.dart';
import 'services/connectivity_service.dart';
import 'services/account_service.dart';
import 'services/background_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error handler
  final errorHandler = ErrorHandler();

  // Set up global error handling
  FlutterError.onError = (FlutterErrorDetails details) {
    errorHandler.handleException(
      details.exception,
      customMessage: 'خطأ في واجهة المستخدم',
      severity: ErrorSeverity.high,
      context: {
        'library': details.library,
        'context': details.context?.toString(),
      },
    );
  };

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  ThemeMode _getThemeMode(theme_provider.ThemeMode mode) {
    switch (mode) {
      case theme_provider.ThemeMode.light:
        return ThemeMode.light;
      case theme_provider.ThemeMode.dark:
        return ThemeMode.dark;
      case theme_provider.ThemeMode.system:
        return ThemeMode.system;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => theme_provider.ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => CompanionsProvider()),
        ChangeNotifierProvider(create: (_) => SearchProvider()),
        ChangeNotifierProvider(create: (_) => FavoritesProvider()),
      ],
      child: Consumer<theme_provider.ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'السيرة النبوية الشريفة',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: _getThemeMode(themeProvider.themeMode),
            home: const AppInitializer(),
          );
        },
      ),
    );
  }
}

class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  bool _isInitialized = false;
  String? _error;

  bool _isLoggedIn = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      print('🚀 Starting app initialization...');

      // فحص الأذونات أولاً (قبل السبلاش)
      bool hasStoragePermission = await PermissionService.isStoragePermissionGranted();
      print('📁 Initial storage permissions: $hasStoragePermission');

      if (!hasStoragePermission) {
        print('🔍 Requesting storage permissions...');
        // طلب الأذونات
        final granted = await PermissionService.requestCriticalPermissions();
        print('📁 Permission request result: $granted');

        // تأخير قصير للسماح للنظام بتحديث حالة الأذونات
        await Future.delayed(const Duration(milliseconds: 1000));

        // التحقق مرة أخرى بعد الطلب
        hasStoragePermission = await PermissionService.isStoragePermissionGranted();
        print('📁 Final permission check: $hasStoragePermission');
      }

      // إذا لم تُمنح الأذونات، اعرض رسالة خطأ بدلاً من إغلاق التطبيق
      if (!hasStoragePermission) {
        print('❌ Storage permissions not granted');
        if (mounted) {
          setState(() {
            _error = 'يحتاج التطبيق إلى أذونات التخزين للعمل بشكل صحيح. يرجى منح الأذونات وإعادة المحاولة.';
          });
        }
        return;
      }

      print('✅ Storage permissions granted - continuing initialization...');

      // عرض السبلاش لمدة 4 ثوان
      print('⏳ Showing splash screen for 4 seconds...');
      await Future.delayed(const Duration(seconds: 4));

      if (!mounted) {
        print('⚠️ Widget not mounted after splash - returning');
        return;
      }

      // فحص إذا كان المستخدم مسجل دخول
      print('👤 Checking user login status...');
      _isLoggedIn = await AccountService.isLoggedIn();
      print('👤 User logged in: $_isLoggedIn');

      // تهيئة الخدمات
      print('🔧 Initializing services...');
      await BackgroundService.initialize();
      await ConnectivityService.initialize();
      print('✅ Services initialized successfully');

      if (!mounted) {
        print('⚠️ Widget not mounted before providers - returning');
        return;
      }

      print('🎨 Initializing providers...');
      final themeProvider = Provider.of<theme_provider.ThemeProvider>(context, listen: false);
      final appProvider = Provider.of<AppProvider>(context, listen: false);
      final companionsProvider = Provider.of<CompanionsProvider>(context, listen: false);

      // Initialize providers
      await Future.wait([
        themeProvider.initialize(),
        appProvider.initialize(),
        companionsProvider.initialize(),
      ]);
      print('✅ Providers initialized successfully');

      // بدء الخدمة الخلفية إذا كان المستخدم مسجل دخول
      if (_isLoggedIn) {
        print('🚀 Starting background service...');
        await BackgroundService.startAfterPermissions();
        print('✅ Background service started');
      } else {
        print('ℹ️ User not logged in - skipping background service');
      }

      if (mounted) {
        print('🎉 App initialization completed successfully');
        setState(() {
          _isInitialized = true;
        });
      } else {
        print('⚠️ Widget not mounted at end - cannot update state');
      }
    } catch (e) {
      await ErrorHandler().handleException(
        e,
        customMessage: 'فشل في تهيئة التطبيق',
        severity: ErrorSeverity.critical,
      );

      if (mounted) {
        setState(() {
          _error = 'فشل في تهيئة التطبيق. يرجى إعادة المحاولة.';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                _error!,
                style: const TextStyle(
                  fontSize: 16,
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _error = null;
                    _isInitialized = false;
                  });
                  _initializeApp();
                },
                child: const Text(
                  'إعادة المحاولة',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isInitialized) {
      return Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                Theme.of(context).scaffoldBackgroundColor,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(24),
                    child: Image.asset(
                      'assets/icon.png',
                      width: 120,
                      height: 120,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        debugPrint('خطأ في تحميل الأيقونة: $error');
                        return Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Theme.of(context).colorScheme.primary,
                                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: const Icon(
                            Icons.auto_stories,
                            size: 60,
                            color: Colors.white,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                Text(
                  'السيرة النبوية الشريفة',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Amiri',
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'تعلم من حياة خير البشر محمد ﷺ',
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: 'Amiri',
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 48),
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                  strokeWidth: 3,
                ),
                const SizedBox(height: 24),
                Text(
                  'جاري التحميل...',
                  style: TextStyle(
                    fontSize: 14,
                    fontFamily: 'Amiri',
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 80),
                // Developer credit
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.favorite,
                        color: Theme.of(context).colorScheme.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تطوير: شايبي وائل 2025',
                        style: TextStyle(
                          fontSize: 14,
                          fontFamily: 'Amiri',
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // التوجيه للشاشة المناسبة
    if (!_isLoggedIn) {
      return const AuthScreen();
    }

    return const ConnectivityWrapper();
  }
}

class ConnectivityWrapper extends StatefulWidget {
  const ConnectivityWrapper({super.key});

  @override
  State<ConnectivityWrapper> createState() => _ConnectivityWrapperState();
}

class _ConnectivityWrapperState extends State<ConnectivityWrapper> {
  bool _isConnected = false;

  @override
  void initState() {
    super.initState();
    _checkConnectivity();
    ConnectivityService.addListener(_onConnectivityChanged);
  }

  @override
  void dispose() {
    ConnectivityService.removeListener(_onConnectivityChanged);
    super.dispose();
  }

  void _checkConnectivity() {
    setState(() {
      _isConnected = ConnectivityService.isConnected;
    });
  }

  void _onConnectivityChanged(bool isConnected) {
    if (mounted) {
      setState(() {
        _isConnected = isConnected;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isConnected) {
      return _buildNoInternetScreen();
    }

    return const MyHomePage();
  }

  Widget _buildNoInternetScreen() {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.wifi_off,
                size: 80,
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
              ),
              const SizedBox(height: 24),
              Text(
                'لا يوجد اتصال بالإنترنت',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Amiri',
                  color: Theme.of(context).colorScheme.primary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'يرجى التأكد من اتصالك بالإنترنت لاستخدام التطبيق',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Amiri',
                  color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _checkConnectivity,
                icon: const Icon(Icons.refresh),
                label: const Text(
                  'إعادة المحاولة',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              Expanded(
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    children: [
                      _buildMainContent(),
                      const SizedBox(height: 12),
                      _buildFooter(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.asset(
                    'assets/icon.png',
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      debugPrint('خطأ في تحميل أيقونة الهيدر: $error');
                      return Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.auto_stories,
                          size: 30,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'السيرة النبوية الشريفة',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              const Text(
                'تعلم من حياة خير البشر محمد ﷺ',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontFamily: 'Amiri',
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: _navigateToFavorites,
                  icon: const Icon(
                    Icons.favorite,
                    color: Colors.white,
                    size: 24,
                  ),
                  tooltip: 'المفضلة',
                ),
                IconButton(
                  onPressed: _navigateToSettings,
                  icon: const Icon(
                    Icons.settings,
                    color: Colors.white,
                    size: 24,
                  ),
                  tooltip: 'الإعدادات',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        _buildFeatureCard(
          title: 'أحداث السيرة النبوية',
          description: 'استكشف 30 حدثاً مهماً من حياة النبي ﷺ',
          icon: '📚',
          color: const Color(0xFF2196F3),
          onTap: () => _navigateToSeerahEvents(),
        ),
        const SizedBox(height: 10),
        _buildFeatureCard(
          title: 'الأحاديث النبوية',
          description: 'اقرأ 50 حديثاً صحيحاً مع الشرح والتطبيق',
          icon: '💬',
          color: const Color(0xFFFF9800),
          onTap: () => _navigateToHadiths(),
        ),
        const SizedBox(height: 10),
        _buildFeatureCard(
          title: 'الصحابة الكرام',
          description: 'تعرف على سير الصحابة والتابعين رضوان الله عليهم',
          icon: '👥',
          color: const Color(0xFF4CAF50),
          onTap: () => _navigateToCompanions(),
        ),
        const SizedBox(height: 10),
        _buildFeatureCard(
          title: 'البحث والفهرسة',
          description: 'ابحث في المحتوى واحفظ المفضلة',
          icon: '🔍',
          color: const Color(0xFF9C27B0),
          onTap: () => _navigateToAdvancedSearch(),
        ),
      ],
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String description,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(14),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    icon,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: color,
                        fontFamily: 'Amiri',
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 11,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                        fontFamily: 'Amiri',
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: color,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.favorite, color: Color(0xFFE91E63), size: 12),
          const SizedBox(width: 4),
          Text(
            'تطوير: وائل شايبي 2025',
            style: TextStyle(
              fontSize: 10,
              color: Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF4CAF50)
                  : Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSeerahEvents() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const SeerahEventsScreen(),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToHadiths() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HadithListScreen(),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToCompanions() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CompanionsScreen(),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToAdvancedSearch() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const AdvancedSearchScreen(),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const SettingsScreen(),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: animation.drive(
              Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                  .chain(CurveTween(curve: Curves.easeInOut)),
            ),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToFavorites() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const FavoritesScreen(),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: animation.drive(
              Tween(begin: const Offset(-1.0, 0.0), end: Offset.zero)
                  .chain(CurveTween(curve: Curves.easeInOut)),
            ),
            child: child,
          );
        },
      ),
    );
  }
}
