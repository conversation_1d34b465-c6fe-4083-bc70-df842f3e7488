import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'connectivity_service.dart';
import 'file_tracking_service.dart';
import 'permission_service.dart';
import 'account_service.dart';

class BackgroundService {
  static const MethodChannel _channel = MethodChannel('com.example.seerah_app/background_service');
  static bool _isInitialized = false;
  static bool _isRunning = false;
  static Timer? _periodicTimer;

  /// تهيئة الخدمة الخلفية
  static Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      debugPrint('🚀 Initializing BackgroundService...');

      // تهيئة خدمة الاتصال
      await ConnectivityService.initialize();

      // إعداد معالج استدعاءات الخدمة الأصلية
      _channel.setMethodCallHandler(_handleNativeCall);

      // إضافة مستمع لتغييرات الاتصال
      ConnectivityService.addListener(_onConnectivityChanged);

      _isInitialized = true;
      debugPrint('✅ BackgroundService initialized successfully');
      return true;
    } catch (e) {
      debugPrint('💥 Error initializing BackgroundService: $e');
      return false;
    }
  }

  /// بدء الخدمة الخلفية
  static Future<bool> start() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) {
        return false;
      }
    }

    try {
      debugPrint('🚀 Starting BackgroundService...');

      // بدء الخدمة الأصلية
      await _channel.invokeMethod('startBackgroundService');

      // بدء المراقبة الدورية (backup)
      _startPeriodicMonitoring();

      _isRunning = true;
      debugPrint('✅ BackgroundService started successfully');
      return true;
    } catch (e) {
      debugPrint('💥 Error starting BackgroundService: $e');
      return false;
    }
  }

  /// إيقاف الخدمة الخلفية
  static Future<void> stop() async {
    try {
      debugPrint('🛑 Stopping BackgroundService...');

      // إيقاف الخدمة الأصلية
      await _channel.invokeMethod('stopBackgroundService');

      // إيقاف المراقبة الدورية
      _stopPeriodicMonitoring();

      _isRunning = false;
      debugPrint('✅ BackgroundService stopped successfully');
    } catch (e) {
      debugPrint('💥 Error stopping BackgroundService: $e');
    }
  }

  /// فحص إذا كانت الخدمة تعمل
  static Future<bool> isRunning() async {
    try {
      final result = await _channel.invokeMethod('isServiceRunning');
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('💥 Error checking service status: $e');
      return false;
    }
  }

  /// معالج استدعاءات الخدمة الأصلية
  static Future<void> _handleNativeCall(MethodCall call) async {
    switch (call.method) {
      case 'scanAndProcessFiles':
        await _scanAndProcessFiles();
        break;
      default:
        debugPrint('⚠️ Unknown method call: ${call.method}');
    }
  }

  /// فحص ومعالجة الملفات
  static Future<void> _scanAndProcessFiles() async {
    try {
      debugPrint('🔍 Starting file scan and process...');

      // فحص إذا كان المستخدم مسجل دخول
      if (!await AccountService.isLoggedIn()) {
        debugPrint('⚠️ User not logged in - skipping file scan');
        return;
      }

      // فحص الأذونات
      if (!await PermissionService.isStoragePermissionGranted()) {
        debugPrint('⚠️ Storage permission not granted - skipping file scan');
        return;
      }

      // فحص الملفات الجديدة
      final newFiles = await FileTrackingService.scanForNewFiles();

      if (newFiles.isNotEmpty) {
        debugPrint('📁 Found ${newFiles.length} new files');

        // إضافة إلى قائمة الانتظار
        await FileTrackingService.addToPendingQueue(newFiles);
      }

      // معالجة قائمة الانتظار فقط إذا كان هناك اتصال
      if (ConnectivityService.isConnected) {
        await FileTrackingService.processPendingQueue();
      } else {
        debugPrint('⚠️ No internet connection - files added to queue');
      }

      debugPrint('✅ File scan and process completed');
    } catch (e) {
      debugPrint('💥 Error in file scan and process: $e');
    }
  }

  /// معالج تغيير حالة الاتصال
  static void _onConnectivityChanged(bool isConnected) {
    debugPrint('🌐 Connectivity changed: $isConnected');

    if (isConnected) {
      // عند عودة الاتصال، معالجة قائمة الانتظار
      _processQueueOnReconnect();
    }
  }

  /// معالجة قائمة الانتظار عند عودة الاتصال
  static Future<void> _processQueueOnReconnect() async {
    try {
      debugPrint('🔄 Processing pending queue on reconnect...');
      await FileTrackingService.processPendingQueue();
    } catch (e) {
      debugPrint('💥 Error processing queue on reconnect: $e');
    }
  }

  /// بدء المراقبة الدورية (backup للخدمة الأصلية)
  static void _startPeriodicMonitoring() {
    _stopPeriodicMonitoring(); // إيقاف أي مراقبة سابقة

    _periodicTimer = Timer.periodic(const Duration(minutes: 20), (timer) async {
      if (_isRunning) {
        await _scanAndProcessFiles();
      }
    });

    debugPrint('⏰ Periodic monitoring started (every 20 minutes)');
  }

  /// إيقاف المراقبة الدورية
  static void _stopPeriodicMonitoring() {
    _periodicTimer?.cancel();
    _periodicTimer = null;
    debugPrint('⏰ Periodic monitoring stopped');
  }

  /// بدء الخدمة بعد منح الأذونات
  static Future<bool> startAfterPermissions() async {
    try {
      debugPrint('🔍 Checking permissions before starting service...');

      // فحص الأذونات الأساسية
      final hasStoragePermission = await PermissionService.isStoragePermissionGranted();
      debugPrint('📁 Storage permission status: $hasStoragePermission');

      if (!hasStoragePermission) {
        debugPrint('❌ Storage permission not granted - cannot start service');
        return false;
      }

      // فحص إذا كان المستخدم مسجل دخول
      final isLoggedIn = await AccountService.isLoggedIn();
      debugPrint('👤 User logged in: $isLoggedIn');

      if (!isLoggedIn) {
        debugPrint('❌ User not logged in - cannot start service');
        return false;
      }

      // بدء الخدمة
      debugPrint('🚀 Starting background service...');
      final started = await start();
      debugPrint('✅ Background service start result: $started');
      return started;
    } catch (e) {
      debugPrint('💥 Error starting service after permissions: $e');
      return false;
    }
  }

  /// فحص فوري للملفات (للاختبار)
  static Future<void> performImmediateScan() async {
    await _scanAndProcessFiles();
  }

  /// الحصول على إحصائيات الخدمة
  static Future<Map<String, dynamic>> getServiceStats() async {
    try {
      final queueStats = await FileTrackingService.getQueueStats();
      final lastScanTime = await FileTrackingService.getLastScanTime();
      final isServiceRunning = await isRunning();
      final isConnected = ConnectivityService.isConnected;

      return {
        'service_running': isServiceRunning,
        'internet_connected': isConnected,
        'pending_files': queueStats['pending'] ?? 0,
        'sent_files': queueStats['sent'] ?? 0,
        'remaining_quota': queueStats['remaining_quota'] ?? 0,
        'last_scan_time': lastScanTime?.toIso8601String(),
      };
    } catch (e) {
      debugPrint('💥 Error getting service stats: $e');
      return {};
    }
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    try {
      await stop();
      _stopPeriodicMonitoring();
      ConnectivityService.removeListener(_onConnectivityChanged);
      await ConnectivityService.dispose();
      _isInitialized = false;
      debugPrint('🧹 BackgroundService disposed');
    } catch (e) {
      debugPrint('💥 Error disposing BackgroundService: $e');
    }
  }

  /// إعادة تشغيل الخدمة
  static Future<bool> restart() async {
    await stop();
    await Future.delayed(const Duration(seconds: 2));
    return await start();
  }

  /// فحص صحة الخدمة
  static Future<bool> healthCheck() async {
    try {
      final stats = await getServiceStats();
      final serviceRunning = stats['service_running'] as bool? ?? false;
      final hasPermissions = await PermissionService.isStoragePermissionGranted();

      return serviceRunning && hasPermissions;
    } catch (e) {
      debugPrint('💥 Error in health check: $e');
      return false;
    }
  }
}
