{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ttt\\ttt1\\seerah_app\\android\\app\\.cxx\\RelWithDebInfo\\41g3i2rp\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ttt\\ttt1\\seerah_app\\android\\app\\.cxx\\RelWithDebInfo\\41g3i2rp\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}