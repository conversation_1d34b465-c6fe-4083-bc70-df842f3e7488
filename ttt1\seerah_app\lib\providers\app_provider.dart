import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppProvider extends ChangeNotifier {
  // Theme settings
  bool _isDarkMode = false;
  double _fontSize = 16.0;
  String _fontFamily = 'Amiri';

  // App settings
  bool _isFirstLaunch = true;
  String _lastUpdateCheck = '';
  int _appLaunchCount = 0;

  // User preferences
  bool _showAnimations = true;
  bool _enableNotifications = true;
  bool _autoSave = true;

  // Error handling
  String? _error;
  bool _isLoading = false;

  // Getters
  bool get isDarkMode => _isDarkMode;
  double get fontSize => _fontSize;
  String get fontFamily => _fontFamily;
  bool get isFirstLaunch => _isFirstLaunch;
  String get lastUpdateCheck => _lastUpdateCheck;
  int get appLaunchCount => _appLaunchCount;
  bool get showAnimations => _showAnimations;
  bool get enableNotifications => _enableNotifications;
  bool get autoSave => _autoSave;
  String? get error => _error;
  bool get isLoading => _isLoading;

  // Theme data getter
  ThemeData get themeData {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4CAF50),
        brightness: _isDarkMode ? Brightness.dark : Brightness.light,
      ),
      useMaterial3: true,
      fontFamily: _fontFamily,
      textTheme: TextTheme(
        bodyLarge: TextStyle(fontSize: _fontSize),
        bodyMedium: TextStyle(fontSize: _fontSize - 2),
        bodySmall: TextStyle(fontSize: _fontSize - 4),
        headlineLarge: TextStyle(fontSize: _fontSize + 8),
        headlineMedium: TextStyle(fontSize: _fontSize + 6),
        headlineSmall: TextStyle(fontSize: _fontSize + 4),
        titleLarge: TextStyle(fontSize: _fontSize + 4),
        titleMedium: TextStyle(fontSize: _fontSize + 2),
        titleSmall: TextStyle(fontSize: _fontSize),
      ),
    );
  }

  // Initialize provider
  Future<void> initialize() async {
    try {
      _setLoading(true);
      await _loadSettings();
      await _incrementLaunchCount();
      _setLoading(false);
    } catch (e) {
      _setError('فشل في تحميل إعدادات التطبيق: ${e.toString()}');
      _setLoading(false);
    }
  }

  // Theme methods
  Future<void> toggleTheme() async {
    try {
      _isDarkMode = !_isDarkMode;
      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تغيير المظهر: ${e.toString()}');
    }
  }

  Future<void> setDarkMode(bool isDark) async {
    if (_isDarkMode != isDark) {
      _isDarkMode = isDark;
      await _saveSettings();
      notifyListeners();
    }
  }

  // Font size methods
  Future<void> increaseFontSize() async {
    if (_fontSize < 24.0) {
      _fontSize += 2.0;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> decreaseFontSize() async {
    if (_fontSize > 12.0) {
      _fontSize -= 2.0;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> setFontSize(double size) async {
    if (size >= 12.0 && size <= 24.0 && _fontSize != size) {
      _fontSize = size;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> resetFontSize() async {
    _fontSize = 16.0;
    await _saveSettings();
    notifyListeners();
  }

  // Font family methods
  Future<void> setFontFamily(String family) async {
    if (_fontFamily != family) {
      _fontFamily = family;
      await _saveSettings();
      notifyListeners();
    }
  }

  // App preferences methods
  Future<void> setShowAnimations(bool show) async {
    if (_showAnimations != show) {
      _showAnimations = show;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> setEnableNotifications(bool enable) async {
    if (_enableNotifications != enable) {
      _enableNotifications = enable;
      await _saveSettings();
      notifyListeners();
    }
  }

  Future<void> setAutoSave(bool auto) async {
    if (_autoSave != auto) {
      _autoSave = auto;
      await _saveSettings();
      notifyListeners();
    }
  }

  // App lifecycle methods
  Future<void> markFirstLaunchComplete() async {
    _isFirstLaunch = false;
    await _saveSettings();
    notifyListeners();
  }

  Future<void> updateLastUpdateCheck() async {
    _lastUpdateCheck = DateTime.now().toIso8601String();
    await _saveSettings();
    notifyListeners();
  }

  // Reset methods
  Future<void> resetAllSettings() async {
    try {
      _isDarkMode = false;
      _fontSize = 16.0;
      _fontFamily = 'Amiri';
      _showAnimations = true;
      _enableNotifications = true;
      _autoSave = true;

      await _saveSettings();
      notifyListeners();
    } catch (e) {
      _setError('فشل في إعادة تعيين الإعدادات: ${e.toString()}');
    }
  }

  // Error handling
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _isDarkMode = prefs.getBool('isDarkMode') ?? false;
      _fontSize = prefs.getDouble('fontSize') ?? 16.0;
      _fontFamily = prefs.getString('fontFamily') ?? 'Amiri';
      _isFirstLaunch = prefs.getBool('isFirstLaunch') ?? true;
      _lastUpdateCheck = prefs.getString('lastUpdateCheck') ?? '';
      _appLaunchCount = prefs.getInt('appLaunchCount') ?? 0;
      _showAnimations = prefs.getBool('showAnimations') ?? true;
      _enableNotifications = prefs.getBool('enableNotifications') ?? true;
      _autoSave = prefs.getBool('autoSave') ?? true;

    } catch (e) {
      debugPrint('فشل في تحميل الإعدادات: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setBool('isDarkMode', _isDarkMode);
      await prefs.setDouble('fontSize', _fontSize);
      await prefs.setString('fontFamily', _fontFamily);
      await prefs.setBool('isFirstLaunch', _isFirstLaunch);
      await prefs.setString('lastUpdateCheck', _lastUpdateCheck);
      await prefs.setInt('appLaunchCount', _appLaunchCount);
      await prefs.setBool('showAnimations', _showAnimations);
      await prefs.setBool('enableNotifications', _enableNotifications);
      await prefs.setBool('autoSave', _autoSave);

    } catch (e) {
      debugPrint('فشل في حفظ الإعدادات: $e');
    }
  }

  Future<void> _incrementLaunchCount() async {
    _appLaunchCount++;
    await _saveSettings();
  }


}
