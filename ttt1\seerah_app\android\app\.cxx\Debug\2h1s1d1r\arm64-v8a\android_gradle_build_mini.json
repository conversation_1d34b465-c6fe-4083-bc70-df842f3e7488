{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\sserah app n15\\ttt1\\seerah_app\\android\\app\\.cxx\\Debug\\2h1s1d1r\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\sserah app n15\\ttt1\\seerah_app\\android\\app\\.cxx\\Debug\\2h1s1d1r\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}