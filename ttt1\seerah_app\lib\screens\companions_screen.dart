import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/companion.dart';
import '../providers/companions_provider.dart';
import '../widgets/companion_card.dart';
import '../widgets/companion_category_chip.dart';
import '../screens/companion_detail_screen.dart';

class CompanionsScreen extends StatefulWidget {
  const CompanionsScreen({super.key});

  @override
  State<CompanionsScreen> createState() => _CompanionsScreenState();
}

class _CompanionsScreenState extends State<CompanionsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _searchController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _searchAnimation;

  bool _isSearchVisible = false;
  bool _isLoading = true;
  final TextEditingController _searchTextController = TextEditingController();
  final List<String> _categories = [
    'الكل',
    'العشرة المبشرون بالجنة',
    'أمهات المؤمنين',
    'الأنصار',
    'المهاجرون',
    'الصحابيات',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() => _isLoading = false);

    // Start animation after loading
    _animationController.forward();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _searchController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchController,
      curve: Curves.easeOut,
    ));
  }

  void _toggleSearch() {
    final provider = Provider.of<CompanionsProvider>(context, listen: false);

    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (!_isSearchVisible) {
        _searchTextController.clear();
        provider.clearSearch();
      }
    });

    if (_isSearchVisible) {
      _searchController.forward();
    } else {
      _searchController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CompanionsProvider>(
      builder: (context, provider, child) {
        if (provider.error != null) {
          return Scaffold(
            appBar: _buildAppBar(),
            body: _buildErrorWidget(provider),
          );
        }

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: _buildAppBar(),
          body: _isLoading ? _buildLoadingWidget() : _buildBody(provider),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.6),
            ],
          ),
        ),
      ),
      title: const Text(
        'الصحابة الكرام',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Amiri',
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(
            _isSearchVisible ? Icons.search_off : Icons.search,
            color: Colors.white,
          ),
          onPressed: _toggleSearch,
        ),
        IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white),
          onPressed: _showFilterDialog,
        ),
      ],
    );
  }

  Widget _buildErrorWidget(CompanionsProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            provider.error!,
            style: const TextStyle(
              fontSize: 16,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => provider.refresh(),
            child: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الصحابة الكرام...',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(CompanionsProvider provider) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          if (_isSearchVisible) _buildSearchBar(provider),
          _buildStatsHeader(provider),
          _buildCategoryFilters(provider),
          Expanded(child: _buildCompanionsList(provider)),
        ],
      ),
    );
  }

  Widget _buildSearchBar(CompanionsProvider provider) {
    return SlideTransition(
      position: _searchAnimation.drive(
        Tween<Offset>(
          begin: const Offset(0, -1),
          end: Offset.zero,
        ),
      ),
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchTextController,
          decoration: InputDecoration(
            hintText: 'ابحث في الاسم أو السيرة أو اللقب...',
            hintStyle: const TextStyle(fontFamily: 'Amiri'),
            border: InputBorder.none,
            icon: Icon(Icons.search, color: Theme.of(context).colorScheme.primary),
          ),
          style: const TextStyle(fontFamily: 'Amiri'),
          onChanged: (value) {
            provider.setSearchQuery(value);
          },
        ),
      ),
    );
  }

  Widget _buildStatsHeader(CompanionsProvider provider) {
    final stats = provider.companionsStats;
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF4CAF50), Color(0xFF66BB6A)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('إجمالي الصحابة', '${stats['الكل'] ?? 0}', Icons.people),
          _buildStatItem('المعروضون', '${provider.filteredCompanions.length}', Icons.visibility),
          _buildStatItem('الفئات', '${_categories.length - 1}', Icons.category),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Amiri',
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontFamily: 'Amiri',
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilters(CompanionsProvider provider) {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: CompanionCategoryChip(
              label: category,
              isSelected: provider.selectedCategory == category,
              onSelected: (selected) {
                if (selected) {
                  provider.setCategory(category);
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildCompanionsList(CompanionsProvider provider) {
    if (provider.filteredCompanions.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: provider.filteredCompanions.length,
      itemBuilder: (context, index) {
        final companion = provider.filteredCompanions[index];
        return CompanionCard(
          companion: companion,
          onTap: () => _navigateToCompanionDetail(companion),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد صحابة تطابق البحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو الفلترة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }



  void _showFilterDialog() {
    final provider = Provider.of<CompanionsProvider>(context, listen: false);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الصحابة', style: TextStyle(fontFamily: 'Amiri')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _categories.map((category) {
            return RadioListTile<String>(
              title: Text(category, style: const TextStyle(fontFamily: 'Amiri')),
              value: category,
              groupValue: provider.selectedCategory,
              onChanged: (value) {
                provider.setCategory(value!);
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _navigateToCompanionDetail(Companion companion) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            CompanionDetailScreen(companion: companion),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: animation.drive(
              Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                  .chain(CurveTween(curve: Curves.easeInOut)),
            ),
            child: child,
          );
        },
      ),
    );
  }
}
