import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum ErrorType {
  network,
  storage,
  validation,
  permission,
  unknown,
}

enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

class AppError {
  final String id;
  final String message;
  final String? details;
  final ErrorType type;
  final ErrorSeverity severity;
  final DateTime timestamp;
  final String? stackTrace;
  final Map<String, dynamic>? context;

  AppError({
    required this.id,
    required this.message,
    this.details,
    required this.type,
    required this.severity,
    required this.timestamp,
    this.stackTrace,
    this.context,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'details': details,
      'type': type.toString(),
      'severity': severity.toString(),
      'timestamp': timestamp.toIso8601String(),
      'stackTrace': stackTrace,
      'context': context,
    };
  }

  factory AppError.fromJson(Map<String, dynamic> json) {
    return AppError(
      id: json['id'],
      message: json['message'],
      details: json['details'],
      type: ErrorType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => ErrorType.unknown,
      ),
      severity: ErrorSeverity.values.firstWhere(
        (e) => e.toString() == json['severity'],
        orElse: () => ErrorSeverity.medium,
      ),
      timestamp: DateTime.parse(json['timestamp']),
      stackTrace: json['stackTrace'],
      context: json['context'],
    );
  }
}

class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  final List<AppError> _errors = [];
  final int _maxErrors = 100;

  // Error logging
  Future<void> logError(
    String message, {
    String? details,
    ErrorType type = ErrorType.unknown,
    ErrorSeverity severity = ErrorSeverity.medium,
    String? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    final error = AppError(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      message: message,
      details: details,
      type: type,
      severity: severity,
      timestamp: DateTime.now(),
      stackTrace: stackTrace,
      context: context,
    );

    _errors.insert(0, error);

    // Keep only recent errors
    if (_errors.length > _maxErrors) {
      _errors.removeRange(_maxErrors, _errors.length);
    }

    // Log to console in debug mode
    if (kDebugMode) {
      debugPrint('🚨 خطأ في التطبيق: $message');
      if (details != null) debugPrint('📝 التفاصيل: $details');
      if (stackTrace != null) debugPrint('📍 المسار: $stackTrace');
    }

    // Save to local storage for critical errors
    if (severity == ErrorSeverity.critical) {
      await _saveErrorToStorage(error);
    }

    // Handle error based on severity
    await _handleErrorBySeverity(error);
  }

  // Handle exceptions
  Future<void> handleException(
    dynamic exception, {
    String? customMessage,
    ErrorType type = ErrorType.unknown,
    ErrorSeverity severity = ErrorSeverity.medium,
    Map<String, dynamic>? context,
  }) async {
    String message = customMessage ?? 'حدث خطأ غير متوقع';
    String? details;
    String? stackTrace;

    if (exception is Exception) {
      details = exception.toString();
    } else if (exception is Error) {
      details = exception.toString();
      stackTrace = exception.stackTrace?.toString();
    } else {
      details = exception.toString();
    }

    await logError(
      message,
      details: details,
      type: type,
      severity: severity,
      stackTrace: stackTrace,
      context: context,
    );
  }

  // Network error handler
  Future<void> handleNetworkError(dynamic error) async {
    String message = 'فشل في الاتصال بالشبكة';
    ErrorSeverity severity = ErrorSeverity.medium;

    if (error.toString().contains('timeout')) {
      message = 'انتهت مهلة الاتصال';
    } else if (error.toString().contains('connection')) {
      message = 'فشل في الاتصال بالخادم';
    } else if (error.toString().contains('404')) {
      message = 'المورد المطلوب غير موجود';
    } else if (error.toString().contains('500')) {
      message = 'خطأ في الخادم';
      severity = ErrorSeverity.high;
    }

    await logError(
      message,
      details: error.toString(),
      type: ErrorType.network,
      severity: severity,
    );
  }

  // Storage error handler
  Future<void> handleStorageError(dynamic error) async {
    await logError(
      'فشل في الوصول إلى التخزين المحلي',
      details: error.toString(),
      type: ErrorType.storage,
      severity: ErrorSeverity.medium,
    );
  }

  // Validation error handler
  Future<void> handleValidationError(String field, String message) async {
    await logError(
      'خطأ في التحقق من البيانات',
      details: '$field: $message',
      type: ErrorType.validation,
      severity: ErrorSeverity.low,
    );
  }

  // Permission error handler
  Future<void> handlePermissionError(String permission) async {
    await logError(
      'لا توجد صلاحية للوصول',
      details: 'مطلوب صلاحية: $permission',
      type: ErrorType.permission,
      severity: ErrorSeverity.medium,
    );
  }

  // Show error dialog
  static void showErrorDialog(
    BuildContext context,
    String message, {
    String? title,
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title ?? 'خطأ',
          style: const TextStyle(
            fontFamily: 'Amiri',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Amiri'),
        ),
        actions: [
          if (onRetry != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text(
                'إعادة المحاولة',
                style: TextStyle(fontFamily: 'Amiri'),
              ),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'موافق',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }

  // Show error snackbar
  static void showErrorSnackBar(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(fontFamily: 'Amiri'),
        ),
        duration: duration,
        action: onRetry != null
            ? SnackBarAction(
                label: 'إعادة المحاولة',
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  // Get errors
  List<AppError> getErrors({ErrorSeverity? severity, ErrorType? type}) {
    return _errors.where((error) {
      if (severity != null && error.severity != severity) return false;
      if (type != null && error.type != type) return false;
      return true;
    }).toList();
  }

  // Clear errors
  void clearErrors() {
    _errors.clear();
  }

  // Get error statistics
  Map<String, int> getErrorStats() {
    final stats = <String, int>{};
    
    for (final type in ErrorType.values) {
      stats[type.toString()] = _errors.where((e) => e.type == type).length;
    }
    
    for (final severity in ErrorSeverity.values) {
      stats[severity.toString()] = _errors.where((e) => e.severity == severity).length;
    }
    
    stats['total'] = _errors.length;
    return stats;
  }

  // Private methods
  Future<void> _handleErrorBySeverity(AppError error) async {
    switch (error.severity) {
      case ErrorSeverity.critical:
        // For critical errors, we might want to restart the app or show a critical error screen
        debugPrint('🔴 خطأ حرج: ${error.message}');
        break;
      case ErrorSeverity.high:
        // For high severity errors, log and possibly notify user
        debugPrint('🟠 خطأ عالي: ${error.message}');
        break;
      case ErrorSeverity.medium:
        // For medium errors, just log
        debugPrint('🟡 خطأ متوسط: ${error.message}');
        break;
      case ErrorSeverity.low:
        // For low errors, minimal logging
        debugPrint('🟢 خطأ منخفض: ${error.message}');
        break;
    }
  }

  Future<void> _saveErrorToStorage(AppError error) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errors = prefs.getStringList('critical_errors') ?? [];
      errors.add(error.toJson().toString());
      
      // Keep only last 10 critical errors
      if (errors.length > 10) {
        errors.removeRange(0, errors.length - 10);
      }
      
      await prefs.setStringList('critical_errors', errors);
    } catch (e) {
      debugPrint('فشل في حفظ الخطأ الحرج: $e');
    }
  }
}
