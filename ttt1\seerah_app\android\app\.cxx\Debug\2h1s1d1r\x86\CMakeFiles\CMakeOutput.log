The target system is: Android - 1 - i686
The host system is: Windows - 10.0.26100 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;
Id flags: -c;--target=i686-none-linux-android28 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
Build flags: -g;-DANDROID;-fdata-sections;-ffunction-sections;-funwind-tables;-fstack-protector-strong;-no-canonical-prefixes;-D_FORTIFY_SOURCE=2;-Wformat;-Werror=format-security;;
Id flags: -c;--target=i686-none-linux-android28 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp

Run Build Command(s):D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe cmTC_017a9 && [1/2] Building C object CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: i686-none-linux-android28

Thread model: posix

InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 (in-process)

 "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple i686-none-linux-android28 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu i686 -target-feature +ssse3 -tune-cpu generic -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb "-fdebug-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -resource-dir "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_017a9.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -ferror-limit 19 -femulated-tls -stack-protector 2 -fgnuc-version=4.2.1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o -x c D:/AndroidstudioSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c

clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"

ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 D:\AndroidstudioSDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include

 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android

 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking C executable cmTC_017a9

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: i686-none-linux-android28

Thread model: posix

InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -z now -z relro --hash-style=gnu --eh-frame-hdr -m elf_i386 -pie -dynamic-linker /system/bin/linker -o cmTC_017a9 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o "-LD:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386" -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28 -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl -lc "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android]
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android]
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe cmTC_017a9 && [1/2] Building C object CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: i686-none-linux-android28]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  ignore line: [ (in-process)]
  ignore line: [ "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe" -cc1 -triple i686-none-linux-android28 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu i686 -target-feature +ssse3 -tune-cpu generic -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb "-fdebug-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -resource-dir "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_017a9.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -ferror-limit 19 -femulated-tls -stack-protector 2 -fgnuc-version=4.2.1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o -x c D:/AndroidstudioSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c]
  ignore line: [clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ D:\AndroidstudioSDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include]
  ignore line: [ D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android]
  ignore line: [ D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_017a9]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: i686-none-linux-android28]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  link line: [ "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -z now -z relro --hash-style=gnu --eh-frame-hdr -m elf_i386 -pie -dynamic-linker /system/bin/linker -o cmTC_017a9 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o "-LD:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386" -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28 -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl -lc "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o]
    arg [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld] ==> ignore
    arg [--sysroot=D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_i386] ==> ignore
    arg [-pie] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_017a9] ==> ignore
    arg [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o] ==> obj [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o]
    arg [-LD:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386] ==> dir [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386]
    arg [-LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28] ==> dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28]
    arg [-LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android] ==> dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android]
    arg [-LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--no-undefined-version] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [CMakeFiles/cmTC_017a9.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a] ==> lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a] ==> lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o] ==> obj [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o]
  remove lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
  remove lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
  collapse library dir [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/i386]
  collapse library dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28]
  collapse library dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android]
  collapse library dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o]
  implicit dirs: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/i386;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp

Run Build Command(s):D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe cmTC_39e81 && [1/2] Building CXX object CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: i686-none-linux-android28

Thread model: posix

InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 (in-process)

 "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple i686-none-linux-android28 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu i686 -target-feature +ssse3 -tune-cpu generic -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb "-fdebug-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -resource-dir "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_39e81.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro -ferror-limit 19 -femulated-tls -stack-protector 2 -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o -x c++ D:/AndroidstudioSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp

clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu

ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"

ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1

 D:\AndroidstudioSDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include

 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android

 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking CXX executable cmTC_39e81

Android (12027248, based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)

Target: i686-none-linux-android28

Thread model: posix

InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin

 "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -z now -z relro --hash-style=gnu --eh-frame-hdr -m elf_i386 -pie -dynamic-linker /system/bin/linker -o cmTC_39e81 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o "-LD:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386" -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28 -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o -Bstatic -lc++ -Bdynamic -lm "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl -lc "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android]
    add: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include]
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android]
  collapse include dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/include;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe cmTC_39e81 && [1/2] Building CXX object CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: i686-none-linux-android28]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  ignore line: [ (in-process)]
  ignore line: [ "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe" -cc1 -triple i686-none-linux-android28 -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu i686 -target-feature +ssse3 -tune-cpu generic -debug-info-kind=constructor -dwarf-version=4 -debugger-tuning=gdb "-fdebug-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -v -ffunction-sections -fdata-sections "-fcoverage-compilation-dir=D:/sserah app n15/ttt1/seerah_app/android/app/.cxx/Debug/2h1s1d1r/x86/CMakeFiles/CMakeTmp" -resource-dir "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18" -dependency-file "CMakeFiles\\cmTC_39e81.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -isysroot D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -internal-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1 -internal-isystem "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/include" -internal-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include -internal-externc-isystem D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include -Wformat -fdeprecated-macro -ferror-limit 19 -femulated-tls -stack-protector 2 -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o -x c++ D:/AndroidstudioSDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [clang -cc1 version 18.0.1 based upon LLVM 18.0.1 default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include"]
  ignore line: [ignoring nonexistent directory "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ D:\AndroidstudioSDK\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64/lib/clang/18/include]
  ignore line: [ D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/i686-linux-android]
  ignore line: [ D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_39e81]
  ignore line: [Android (12027248  based on r522817) clang version 18.0.1 (https://android.googlesource.com/toolchain/llvm-project d8003a456d14a3deb8054cdaa529ffbf02d9b262)]
  ignore line: [Target: i686-none-linux-android28]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin]
  link line: [ "D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld" --sysroot=D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -z now -z relro --hash-style=gnu --eh-frame-hdr -m elf_i386 -pie -dynamic-linker /system/bin/linker -o cmTC_39e81 D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o "-LD:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386" -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28 -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android -LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib --build-id=sha1 --no-rosegment --no-undefined-version --fatal-warnings --no-undefined CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o -Bstatic -lc++ -Bdynamic -lm "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl -lc "D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a" -l:libunwind.a -ldl D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o]
    arg [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/bin/ld.lld] ==> ignore
    arg [--sysroot=D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [elf_i386] ==> ignore
    arg [-pie] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_39e81] ==> ignore
    arg [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o] ==> obj [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o]
    arg [-LD:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386] ==> dir [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386]
    arg [-LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28] ==> dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28]
    arg [-LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android] ==> dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android]
    arg [-LD:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--no-rosegment] ==> ignore
    arg [--no-undefined-version] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [CMakeFiles/cmTC_39e81.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-Bstatic] ==> search static
    arg [-lc++] ==> lib [c++]
    arg [-Bdynamic] ==> search dynamic
    arg [-lm] ==> lib [m]
    arg [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a] ==> lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a] ==> lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o] ==> obj [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o]
  remove lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
  remove lib [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/libclang_rt.builtins-i686-android.a]
  collapse library dir [D:\\AndroidstudioSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64/lib/clang/18/lib/linux/i386] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/i386]
  collapse library dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28]
  collapse library dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android]
  collapse library dir [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtbegin_dynamic.o;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28/crtend_android.o]
  implicit dirs: [D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/18/lib/linux/i386;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android/28;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/i686-linux-android;D:/AndroidstudioSDK/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


