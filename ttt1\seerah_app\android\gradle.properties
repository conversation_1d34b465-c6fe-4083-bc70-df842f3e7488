org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
android.enableR8=true
android.enableR8.fullMode=false
org.gradle.caching=true
org.gradle.parallel=true

# Flutter build settings
flutter.buildMode=debug
flutter.versionName=1.0.0
flutter.versionCode=1

# Kotlin settings
kotlin.code.style=official

# Android settings
android.nonTransitiveRClass=false
android.nonFinalResIds=false
android.enableD8.desugaring=true

# NDK settings
android.ndkVersion=25.1.8937393

# Disable file system watching to avoid path issues
org.gradle.vfs.watch=false

# Configuration cache
org.gradle.configuration-cache=false

# Fix path issues
org.gradle.unsafe.configuration-cache=false
org.gradle.unsafe.configuration-cache-problems=warn

# Disable daemon for consistent builds
org.gradle.daemon=false
