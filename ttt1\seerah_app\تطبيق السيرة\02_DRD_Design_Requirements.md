# 🎨 **DRD - وثيقة متطلبات التصميم**
## **تطبيق السيرة النبوية الشريفة**

---

## 🎯 **فلسفة التصميم**

### **المبادئ الأساسية**
- **الأصالة الإسلامية**: تصميم يعكس الهوية الإسلامية العربية
- **البساطة والوضوح**: واجهة نظيفة وسهلة الاستخدام
- **الجمال والأناقة**: ألوان متناسقة وتدرجات جميلة
- **إمكانية الوصول**: دعم كامل للعربية والـ RTL

### **الهوية البصرية**
- **الرمز الأساسي**: 🕌 (مسجد) يرمز للإسلام والعبادة
- **اللون الأساسي**: الأخضر بدرجاته (رمز الإسلام والطبيعة)
- **الطابع العام**: روحاني، تعليمي، محترم، جميل

---

## 🎨 **نظام الألوان الشامل**

### **1. التدرج الأساسي (Primary Gradient)**
```css
الخلفية الرئيسية - تدرج عمودي من الأعلى للأسفل:
- اللون الأول (أعلى): #102c23 (أخضر داكن جداً)
- اللون الثاني: #183f33 (أخضر داكن)
- اللون الثالث: #123127 (أخضر متوسط)
- اللون الرابع (أسفل): #113026 (أخضر فاتح نسبياً)

استخدام: خلفيات الشاشات الرئيسية، AppBar، Drawer Header
```

### **2. ألوان الأقسام (Section Colors)**
```css
السيرة النبوية:
- اللون الأساسي: #4CAF50 (أخضر Material)
- اللون الفاتح: #81C784 (للحالات التفاعلية)
- اللون الداكن: #388E3C (للنصوص والحدود)

الأحاديث النبوية:
- اللون الأساسي: #FF9800 (برتقالي Material)
- اللون الفاتح: #FFB74D (للحالات التفاعلية)
- اللون الداكن: #F57C00 (للنصوص والحدود)

الصحابة الكرام:
- اللون الأساسي: #9C27B0 (بنفسجي Material)
- اللون الفاتح: #BA68C8 (للحالات التفاعلية)
- اللون الداكن: #7B1FA2 (للنصوص والحدود)
```

### **3. ألوان النصوص (Text Colors)**
```css
على الخلفيات الداكنة:
- النص الأساسي: #FFFFFF (أبيض نقي)
- النص الثانوي: #CCFFFFFF (أبيض 80% شفافية)
- النص المساعد: #99FFFFFF (أبيض 60% شفافية)

على الخلفيات البيضاء:
- النص الأساسي: #333333 (رمادي داكن)
- النص الثانوي: #666666 (رمادي متوسط)
- النص المساعد: #999999 (رمادي فاتح)
```

### **4. ألوان الحالات (State Colors)**
```css
النجاح: #4CAF50 (أخضر)
التحذير: #FF9800 (برتقالي)
الخطأ: #F44336 (أحمر)
المعلومات: #2196F3 (أزرق)

الخلفيات:
- البطاقات: #FFFFFF (أبيض نقي)
- الخلفية الثانوية: #F5F5F5 (رمادي فاتح جداً)
- الحدود: #E0E0E0 (رمادي فاتح)
```

---

## 📝 **نظام الخطوط والنصوص**

### **1. الخطوط المستخدمة**
```css
الخط الأساسي:
- Android: Roboto (مع دعم العربية)
- iOS: San Francisco (مع دعم العربية)
- Web: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif

الخط العربي:
- الأولوية الأولى: 'Noto Sans Arabic'
- الأولوية الثانية: 'Dubai'
- الاحتياطي: sans-serif
```

### **2. أحجام النصوص (Typography Scale)**
```css
العناوين الرئيسية (H1): 28sp/px - Bold
العناوين الثانوية (H2): 24sp/px - Bold
العناوين الفرعية (H3): 20sp/px - SemiBold
النص الأساسي (Body1): 16sp/px - Regular
النص الثانوي (Body2): 14sp/px - Regular
النص المساعد (Caption): 12sp/px - Regular
النص الصغير (Small): 10sp/px - Regular
```

### **3. خصائص النصوص العربية**
```css
اتجاه النص: RTL (من اليمين لليسار)
محاذاة النص: TextAlign.right (للعربية)
ارتفاع السطر: 1.3-1.5 (للقراءة المريحة)
المسافة بين الأحرف: 0.5sp (للوضوح)
```

---

## 📐 **نظام التخطيط والمسافات**

### **1. وحدة المسافة الأساسية**
```css
الوحدة الأساسية: 8dp/px
المسافات المستخدمة: 4, 8, 12, 16, 20, 24, 32, 40, 48, 56, 64
```

### **2. مسافات المكونات**
```css
Padding داخلي للبطاقات: 16dp
Margin بين البطاقات: 12dp
Padding للشاشات: 20dp
ارتفاع AppBar: 56dp
ارتفاع Bottom Navigation: 60dp
```

### **3. أحجام المكونات**
```css
أيقونات صغيرة: 16x16dp
أيقونات متوسطة: 24x24dp
أيقونات كبيرة: 32x32dp
أيقونات عملاقة: 48x48dp

أزرار صغيرة: ارتفاع 32dp
أزرار متوسطة: ارتفاع 40dp
أزرار كبيرة: ارتفاع 48dp
```

---

## 🎭 **المكونات والعناصر**

### **1. AppBar (شريط التطبيق)**
```css
الخلفية: التدرج الأساسي (Primary Gradient)
الارتفاع: 56dp + 40dp padding top
المحتوى:
  - زر القائمة (☰): 24dp، أبيض، يسار
  - العنوان: 20sp، Bold، أبيض، وسط
  - زر الإشعارات (🔔): 24dp، أبيض، يمين

الظل: elevation 4dp
الانتقال: 300ms ease-in-out
```

### **2. Bottom Navigation (التنقل السفلي)**
```css
الخلفية: #FFFFFF (أبيض)
الارتفاع: 60dp
عدد التبويبات: 6
المحتوى لكل تبويبة:
  - الأيقونة: 20sp، وسط
  - النص: 10sp، وسط، تحت الأيقونة
  - المسافة الداخلية: 8dp

الحالة النشطة:
  - لون الأيقونة: اللون الأساسي للقسم
  - لون النص: اللون الأساسي للقسم
  - الخط: Bold

الحالة غير النشطة:
  - لون الأيقونة: #757575 (رمادي)
  - لون النص: #757575 (رمادي)
  - الخط: Regular
```

### **3. البطاقات (Cards)**
```css
الخلفية: #FFFFFF (أبيض)
الزوايا المدورة: 12dp
الظل: elevation 2dp
الحدود: لا توجد
المسافة الداخلية: 16dp
المسافة الخارجية: 12dp (بين البطاقات)

انتقال الضغط:
  - التأثير: ripple effect
  - اللون: #1F000000 (أسود 12% شفافية)
  - المدة: 150ms
```

### **4. الأزرار (Buttons)**
```css
الزر الأساسي (Primary Button):
  - الخلفية: لون القسم الأساسي
  - النص: أبيض، 16sp، SemiBold
  - الزوايا: 8dp
  - الارتفاع: 40dp
  - المسافة الداخلية: 20dp أفقي، 12dp عمودي

الزر الثانوي (Secondary Button):
  - الخلفية: شفافة
  - الحدود: 1dp، لون القسم الأساسي
  - النص: لون القسم الأساسي، 16sp، SemiBold
  - باقي الخصائص مثل الأساسي

زر الأيقونة (Icon Button):
  - الخلفية: شفافة
  - الأيقونة: 24dp
  - المنطقة التفاعلية: 48x48dp
  - التأثير: ripple دائري
```

---

## 🖼️ **تصميم الشاشات المفصل**

### **1. الشاشة الرئيسية (Home Screen)**

#### **التخطيط العام:**
```
┌─────────────────────────────────────┐
│ AppBar (التدرج الأخضر)              │
│ ☰ سيرة النبي محمد ﷺ            🔔 │
├─────────────────────────────────────┤
│                                     │
│ [خلفية متدرجة أخضر]                │
│                                     │
│     🕌 (أيقونة كبيرة 80sp)          │
│                                     │
│   سيرة النبي محمد ﷺ                │
│   (28sp، Bold، أبيض، وسط)          │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📖 السيرة النبوية              │ │
│ │ تعرف على أحداث حياة النبي ﷺ    │ │
│ │                            30   │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 💬 الأحاديث النبوية            │ │
│ │ مجموعة من الأحاديث الشريفة     │ │
│ │                            50   │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 👥 الصحابة الكرام              │ │
│ │ تعرف على سير الصحابة الكرام    │ │
│ │                            25   │ │
│ └─────────────────────────────────┘ │
│                                     │
├─────────────────────────────────────┤
│ Bottom Navigation (6 تبويبات)      │
│ 🏠 📖 💬 👥 🔍 ⭐                  │
└─────────────────────────────────────┘
```

#### **تفاصيل كروت الأقسام:**
```css
كارت السيرة النبوية:
  - الخلفية: أبيض
  - الأيقونة: 📖 (32sp) في دائرة خضراء شفافة
  - العنوان: "السيرة النبوية" (18sp، Bold، #333333)
  - الوصف: "تعرف على أحداث حياة النبي ﷺ" (14sp، #666666)
  - العداد: "30" (24sp، Bold، #4CAF50)
  - التخطيط: أفقي (أيقونة + نص + عداد)

كارت الأحاديث النبوية:
  - نفس التصميم مع:
  - الأيقونة: 💬 في دائرة برتقالية شفافة
  - العنوان: "الأحاديث النبوية"
  - الوصف: "مجموعة من الأحاديث الشريفة"
  - العداد: "50" باللون البرتقالي #FF9800

كارت الصحابة الكرام:
  - نفس التصميم مع:
  - الأيقونة: 👥 في دائرة بنفسجية شفافة
  - العنوان: "الصحابة الكرام"
  - الوصف: "تعرف على سير الصحابة الكرام"
  - العداد: "25" باللون البنفسجي #9C27B0
```

### **2. شاشة السيرة النبوية**

#### **التخطيط:**
```
┌─────────────────────────────────────┐
│ AppBar (التدرج الأخضر)              │
│ ← السيرة النبوية                   │
├─────────────────────────────────────┤
│ [خلفية متدرجة أخضر]                │
│                                     │
│ 📚 جميع أحداث حياة النبي ﷺ         │
│ (18sp، Bold، أبيض، وسط)            │
│ (30 حدث)                           │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 👶 ولادة النبي محمد ﷺ           │ │
│ │ 📅 571 م - 📍 مكة المكرمة      │ │
│ │ 📂 الطفولة | ⭐ أهمية: 5/5     │ │
│ │ وُلد النبي محمد ﷺ في مكة...    │ │
│ │ 💡 يمكن إضافته للمفضلة          │ │
│ │                            ⭐   │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [المزيد من البطاقات...]             │
│                                     │
├─────────────────────────────────────┤
│ Bottom Navigation                   │
└─────────────────────────────────────┘
```

#### **تصميم بطاقة الحدث:**
```css
الخلفية: أبيض
الزوايا: 12dp
الظل: elevation 2dp
المسافة الداخلية: 16dp

العنوان:
  - النص: "👶 ولادة النبي محمد ﷺ"
  - الحجم: 16sp
  - الوزن: Bold
  - اللون: #333333

المعلومات الأساسية:
  - النص: "📅 571 م - 📍 مكة المكرمة"
  - الحجم: 14sp
  - اللون: #4CAF50

التصنيف والأهمية:
  - النص: "📂 الطفولة | ⭐ أهمية: 5/5"
  - الحجم: 13sp
  - اللون: #666666

الوصف:
  - النص: "وُلد النبي محمد ﷺ في مكة..."
  - الحجم: 13sp
  - اللون: #555555
  - ارتفاع السطر: 1.3

إشارة المفضلة:
  - النص: "💡 يمكن إضافته للمفضلة"
  - الحجم: 12sp
  - اللون: #888888

زر المفضلة:
  - الأيقونة: ⭐ (16sp)
  - الموقع: أعلى اليمين
  - اللون: #FFD700 (ذهبي)
```

### **3. شاشة الأحاديث النبوية**

#### **تصميم بطاقة الحديث:**
```css
العنوان:
  - النص: "📖 أهمية النية"
  - الحجم: 16sp
  - الوزن: Bold
  - اللون: #333333

النص المختصر:
  - النص: "\"هذا الحديث يؤكد أن قيمة العمل...\""
  - الحجم: 14sp
  - اللون: #555555
  - النمط: مائل (italic)

المصدر والراوي:
  - النص: "📚 صحيح البخاري | 👤 عمر بن الخطاب"
  - الحجم: 13sp
  - اللون: #FF9800

إشارة المفضلة:
  - النص: "⭐ يمكن إضافته للمفضلة"
  - الحجم: 12sp
  - اللون: #888888
```

### **4. شاشة الصحابة الكرام**

#### **تصميم بطاقة الصحابي:**
```css
العنوان:
  - النص: "👑 أبو بكر الصديق"
  - الحجم: 16sp
  - الوزن: Bold
  - اللون: #333333

الكنية:
  - النص: "🏷️ الصديق، العتيق"
  - الحجم: 14sp
  - اللون: #9C27B0

المعلومات الأساسية:
  - النص: "🏠 مكة المكرمة | 👥 قريش - بنو تيم"
  - الحجم: 13sp
  - اللون: #666666

الوصف المختصر:
  - النص: "أول الخلفاء الراشدين وأول من آمن..."
  - الحجم: 13sp
  - اللون: #555555

الصفات المشهورة:
  - النص: "⭐ مشهور بـ: الصدق، الحلم، الكرم..."
  - الحجم: 12sp
  - اللون: #888888
```

---

## 🎭 **الرسوم المتحركة والانتقالات**

### **1. انتقالات الشاشات**
```css
نوع الانتقال: Slide Transition
الاتجاه: من اليمين لليسار (RTL)
المدة: 300ms
المنحنى: Curves.easeInOut
```

### **2. رسوم متحركة للعدادات**
```css
النوع: Tween Animation
المدة: 1500ms
المنحنى: Curves.easeOutCubic
التأثير: العد من 0 إلى الرقم النهائي
التأخير: 500ms بعد فتح الشاشة
```

### **3. تأثيرات التفاعل**
```css
Ripple Effect:
  - اللون: #1F000000 (أسود 12% شفافية)
  - المدة: 150ms
  - النوع: دائري للأزرار، مستطيل للبطاقات

Scale Animation (عند الضغط):
  - المقياس: 0.95
  - المدة: 100ms
  - المنحنى: Curves.easeInOut
```

### **4. تحميل المحتوى**
```css
Shimmer Effect:
  - اللون الأساسي: #E0E0E0
  - اللون المتحرك: #F5F5F5
  - المدة: 1000ms
  - الاتجاه: من اليسار لليمين
```

---

## 📱 **التجاوب والتكيف**

### **1. أحجام الشاشات**
```css
الهواتف الصغيرة (< 360dp):
  - تقليل المسافات بنسبة 25%
  - تصغير الخطوط بـ 2sp
  - تقليل ارتفاع البطاقات

الهواتف المتوسطة (360-480dp):
  - الأحجام الافتراضية
  - التخطيط العادي

الهواتف الكبيرة (> 480dp):
  - زيادة المسافات بنسبة 15%
  - تكبير الخطوط بـ 1sp
  - زيادة عرض البطاقات
```

### **2. الاتجاهات**
```css
الوضع العمودي (Portrait):
  - التخطيط الافتراضي
  - عمود واحد للبطاقات

الوضع الأفقي (Landscape):
  - عمودين للبطاقات
  - تقليل ارتفاع AppBar
  - إخفاء بعض المسافات الزائدة
```

---

## ♿ **إمكانية الوصول**

### **1. دعم قارئ الشاشة**
```css
جميع العناصر التفاعلية:
  - semanticLabel واضح بالعربية
  - hint مفيد للمستخدم
  - excludeSemantics: false

الأيقونات:
  - وصف نصي واضح
  - دور العنصر محدد
```

### **2. التباين العالي**
```css
وضع التباين العالي:
  - زيادة سماكة الحدود إلى 2dp
  - تغيير الألوان لتباين أعلى
  - إزالة الشفافية من النصوص
```

### **3. أحجام الخطوط**
```css
دعم إعدادات النظام:
  - تكبير الخطوط حتى 200%
  - الحفاظ على التخطيط
  - تجنب قطع النصوص
```

---

## 🔧 **إرشادات التطبيق**

### **1. الاتساق**
- استخدام نفس الألوان في جميع أنحاء التطبيق
- الحفاظ على نفس المسافات والأحجام
- توحيد أسلوب الكتابة والرموز

### **2. الوضوح**
- تجنب الازدحام البصري
- استخدام مسافات بيضاء كافية
- ترتيب المعلومات حسب الأهمية

### **3. الجمال**
- استخدام التدرجات بذوق
- تناسق الألوان مع بعضها
- رسوم متحركة سلسة وهادئة

---

## 🎯 **شاشات إضافية مفصلة**

### **5. شاشة المفضلة**

#### **التخطيط:**
```
┌─────────────────────────────────────┐
│ AppBar (التدرج الأخضر)              │
│ ← المفضلة                          │
├─────────────────────────────────────┤
│ [خلفية متدرجة أخضر]                │
│                                     │
│ ⭐ جميع العناصر المفضلة             │
│ (18sp، Bold، أبيض، وسط)            │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📖 السيرة النبوية (5 عناصر)    │ │
│ │ 💬 الأحاديث النبوية (3 عناصر)  │ │
│ │ 👥 الصحابة الكرام (2 عنصر)     │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [قائمة العناصر المفضلة...]          │
│                                     │
├─────────────────────────────────────┤
│ Bottom Navigation                   │
└─────────────────────────────────────┘
```

### **6. شاشة البحث**

#### **التخطيط:**
```
┌─────────────────────────────────────┐
│ AppBar (التدرج الأخضر)              │
│ ← البحث                            │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │ 🔍 ابحث في المحتوى...          │ │
│ │ [حقل البحث مع أيقونة]           │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📂 تصفية النتائج:               │ │
│ │ ☑️ السيرة النبوية              │ │
│ │ ☑️ الأحاديث النبوية            │ │
│ │ ☑️ الصحابة الكرام              │ │
│ └─────────────────────────────────┘ │
│                                     │
│ [نتائج البحث...]                   │
│                                     │
├─────────────────────────────────────┤
│ Bottom Navigation                   │
└─────────────────────────────────────┘
```

### **7. القائمة الجانبية (Drawer)**

#### **التخطيط:**
```
┌─────────────────────────────────────┐
│ [Header - التدرج الأخضر]            │
│                                     │
│     🕌 (أيقونة كبيرة 60sp)          │
│                                     │
│   سيرة النبي محمد ﷺ                │
│   (20sp، Bold، أبيض)               │
│                                     │
│   الإصدار 1.0.0                    │
│   (14sp، أبيض 70% شفافية)          │
│                                     │
├─────────────────────────────────────┤
│ 🏠 الرئيسية                        │
│ 📖 السيرة النبوية                  │
│ 💬 الأحاديث النبوية                │
│ 👥 الصحابة الكرام                  │
│ ⭐ المفضلة                         │
│ 🔍 البحث                           │
│ ─────────────────────────────────── │
│ 🎯 نظام المكافآت                   │
│ 📊 الإحصائيات                      │
│ ⚙️ الإعدادات                       │
│ ─────────────────────────────────── │
│ 📧 تواصل مع المطور                 │
│ ℹ️ حول التطبيق                     │
│ 📄 سياسة الخصوصية                  │
└─────────────────────────────────────┘
```

---

## 🎨 **تفاصيل إضافية للمكونات**

### **1. حقل البحث**
```css
الخلفية: #FFFFFF (أبيض)
الحدود: 1dp، #E0E0E0
الزوايا المدورة: 25dp (شكل حبة دواء)
الارتفاع: 50dp
المسافة الداخلية: 16dp أفقي، 12dp عمودي

الأيقونة:
  - النوع: 🔍
  - الحجم: 20sp
  - اللون: #757575
  - الموقع: يسار النص

النص التوضيحي:
  - النص: "ابحث في المحتوى..."
  - الحجم: 16sp
  - اللون: #999999

النص المدخل:
  - الحجم: 16sp
  - اللون: #333333
  - الخط: Regular
```

### **2. مؤشر التحميل**
```css
النوع: Circular Progress Indicator
الحجم: 24dp
السماكة: 3dp
اللون: لون القسم الأساسي
الخلفية: شفافة
الرسوم المتحركة: دوران مستمر
```

### **3. رسائل الحالة**
```css
رسالة النجاح:
  - الخلفية: #E8F5E8 (أخضر فاتح)
  - النص: #2E7D32 (أخضر داكن)
  - الأيقونة: ✅
  - الحدود: 1dp، #4CAF50

رسالة الخطأ:
  - الخلفية: #FFEBEE (أحمر فاتح)
  - النص: #C62828 (أحمر داكن)
  - الأيقونة: ❌
  - الحدود: 1dp، #F44336

رسالة المعلومات:
  - الخلفية: #E3F2FD (أزرق فاتح)
  - النص: #1565C0 (أزرق داكن)
  - الأيقونة: ℹ️
  - الحدود: 1dp، #2196F3
```

### **4. شريط التقدم**
```css
الخلفية: #E0E0E0 (رمادي فاتح)
التقدم: لون القسم الأساسي
الارتفاع: 4dp
الزوايا المدورة: 2dp
الرسوم المتحركة: تدرجية سلسة
```

---

## 📐 **قياسات دقيقة للعناصر**

### **1. أبعاد البطاقات**
```css
بطاقة الحدث:
  - العرض: match_parent - 24dp (12dp margin × 2)
  - الارتفاع: wrap_content (حد أدنى 120dp)
  - الزوايا: 12dp
  - الظل: elevation 2dp

بطاقة الحديث:
  - العرض: match_parent - 24dp
  - الارتفاع: wrap_content (حد أدنى 140dp)
  - الزوايا: 12dp
  - الظل: elevation 2dp

بطاقة الصحابي:
  - العرض: match_parent - 24dp
  - الارتفاع: wrap_content (حد أدنى 130dp)
  - الزوايا: 12dp
  - الظل: elevation 2dp
```

### **2. مسافات داخلية دقيقة**
```css
AppBar:
  - Padding top: 40dp (status bar)
  - Padding horizontal: 16dp
  - Padding bottom: 8dp

البطاقات:
  - Padding: 16dp جميع الجهات
  - Margin: 12dp أفقي، 8dp عمودي

Bottom Navigation:
  - Padding: 8dp عمودي، 4dp أفقي
  - Item padding: 8dp جميع الجهات
```

### **3. أحجام الأيقونات المحددة**
```css
أيقونات Bottom Navigation: 20sp
أيقونات AppBar: 24sp
أيقونات البطاقات: 16sp
أيقونة التطبيق الرئيسية: 80sp
أيقونات الأقسام في الدوائر: 32sp
أيقونات الحالة: 16sp
```

---

## 🎭 **تفاصيل الرسوم المتحركة**

### **1. انتقال فتح البطاقة**
```css
النوع: Hero Animation
المدة: 300ms
المنحنى: Curves.easeInOut
التأثير: تكبير البطاقة من موقعها إلى ملء الشاشة
```

### **2. رسوم متحركة للقوائم**
```css
النوع: Staggered Animation
التأخير بين العناصر: 100ms
المدة لكل عنصر: 300ms
التأثير: ظهور من الأسفل مع fade in
```

### **3. تأثير الضغط على البطاقات**
```css
Scale Animation:
  - المقياس الأولي: 1.0
  - المقياس عند الضغط: 0.95
  - مدة الانتقال: 100ms
  - العودة للحجم الطبيعي: 150ms
```

---

**🎨 هذه الوثيقة المحدثة تحتوي على جميع متطلبات التصميم بالتفصيل الدقيق والشامل لضمان إعادة البناء البصري بنسبة 100% مطابقة للأصل.**
