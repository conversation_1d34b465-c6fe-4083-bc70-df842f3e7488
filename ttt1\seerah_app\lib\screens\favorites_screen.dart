import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/favorites_provider.dart';
import '../data/seerah_data.dart';
import '../data/hadith_data.dart';
import '../data/companions_data.dart';
import 'seerah_event_detail_screen.dart';
import 'hadith_detail_screen.dart';
import 'companion_detail_screen.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Initialize favorites
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FavoritesProvider>().initializeFavorites();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      body: Consumer<FavoritesProvider>(
        builder: (context, favoritesProvider, child) {
          if (favoritesProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (favoritesProvider.error != null) {
            return _buildErrorWidget(favoritesProvider.error!);
          }

          return Column(
            children: [
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllFavorites(favoritesProvider),
                    _buildFavoritesByType(favoritesProvider, FavoriteType.seerah),
                    _buildFavoritesByType(favoritesProvider, FavoriteType.hadith),
                    _buildFavoritesByType(favoritesProvider, FavoriteType.companion),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
            ],
          ),
        ),
      ),
      title: const Text(
        'المفضلة',
        style: TextStyle(
          color: Colors.white,
          fontFamily: 'Amiri',
          fontWeight: FontWeight.bold,
        ),
      ),
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            if (favoritesProvider.favorites.isNotEmpty) {
              return PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                onSelected: (value) {
                  if (value == 'clear_all') {
                    _showClearAllDialog(favoritesProvider);
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'clear_all',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all, color: Colors.red),
                        SizedBox(width: 8),
                        Text(
                          'مسح الكل',
                          style: TextStyle(
                            fontFamily: 'Amiri',
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        return Container(
          color: Theme.of(context).cardColor,
          child: TabBar(
            controller: _tabController,
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Colors.grey,
            indicatorColor: Theme.of(context).colorScheme.primary,
            labelStyle: const TextStyle(
              fontFamily: 'Amiri',
              fontWeight: FontWeight.bold,
            ),
            unselectedLabelStyle: const TextStyle(
              fontFamily: 'Amiri',
              fontWeight: FontWeight.normal,
            ),
            tabs: [
              Tab(
                text: 'الكل (${favoritesProvider.favorites.length})',
              ),
              Tab(
                text: 'السيرة (${favoritesProvider.getFavoritesCountByType(FavoriteType.seerah)})',
              ),
              Tab(
                text: 'الأحاديث (${favoritesProvider.getFavoritesCountByType(FavoriteType.hadith)})',
              ),
              Tab(
                text: 'الصحابة (${favoritesProvider.getFavoritesCountByType(FavoriteType.companion)})',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAllFavorites(FavoritesProvider favoritesProvider) {
    if (favoritesProvider.favorites.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: favoritesProvider.favorites.length,
      itemBuilder: (context, index) {
        final favorite = favoritesProvider.favorites[index];
        return _buildFavoriteCard(favorite, favoritesProvider);
      },
    );
  }

  Widget _buildFavoritesByType(FavoritesProvider favoritesProvider, FavoriteType type) {
    final favorites = favoritesProvider.getFavoritesByType(type);

    if (favorites.isEmpty) {
      return _buildEmptyStateByType(type);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: favorites.length,
      itemBuilder: (context, index) {
        final favorite = favorites[index];
        return _buildFavoriteCard(favorite, favoritesProvider);
      },
    );
  }

  Widget _buildFavoriteCard(FavoriteItem favorite, FavoritesProvider favoritesProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          child: Icon(
            _getTypeIcon(favorite.type),
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        title: Text(
          favorite.title,
          style: const TextStyle(
            fontFamily: 'Amiri',
            fontWeight: FontWeight.bold,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              favorite.subtitle,
              style: const TextStyle(fontFamily: 'Amiri'),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getTypeName(favorite.type),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.primary,
                      fontFamily: 'Amiri',
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  _formatDate(favorite.addedAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontFamily: 'Amiri',
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.favorite, color: Colors.red),
          onPressed: () {
            favoritesProvider.removeFromFavorites(favorite.id, favorite.type);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text(
                  'تم إزالة العنصر من المفضلة',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 2),
              ),
            );
          },
        ),
        onTap: () => _navigateToDetail(favorite),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مفضلة',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة المحتوى المفضل لديك',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.explore),
            label: const Text(
              'استكشف المحتوى',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStateByType(FavoriteType type) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getTypeIcon(type),
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مفضلة في ${_getTypeName(type)}',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة محتوى ${_getTypeName(type)} المفضل لديك',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.red[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 16,
              color: Colors.red[500],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.read<FavoritesProvider>().initializeFavorites();
            },
            icon: const Icon(Icons.refresh),
            label: const Text(
              'إعادة المحاولة',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(FavoriteType type) {
    switch (type) {
      case FavoriteType.seerah:
        return Icons.auto_stories;
      case FavoriteType.hadith:
        return Icons.menu_book;
      case FavoriteType.companion:
        return Icons.people;
    }
  }

  String _getTypeName(FavoriteType type) {
    switch (type) {
      case FavoriteType.seerah:
        return 'السيرة';
      case FavoriteType.hadith:
        return 'الأحاديث';
      case FavoriteType.companion:
        return 'الصحابة';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _navigateToDetail(FavoriteItem favorite) {
    try {
      switch (favorite.type) {
        case FavoriteType.seerah:
          final event = SeerahData.getAllEvents().firstWhere((e) => e.id == favorite.id);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SeerahEventDetailScreen(event: event),
            ),
          );
          break;
        case FavoriteType.hadith:
          final hadith = HadithData.getAllHadiths().firstWhere((h) => h.id == favorite.id);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => HadithDetailScreen(hadith: hadith),
            ),
          );
          break;
        case FavoriteType.companion:
          final companion = CompanionsData.getAllCompanions().firstWhere((c) => c.id == favorite.id);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CompanionDetailScreen(companion: companion),
            ),
          );
          break;
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'خطأ في فتح التفاصيل: ${e.toString()}',
            style: const TextStyle(fontFamily: 'Amiri'),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showClearAllDialog(FavoritesProvider favoritesProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'مسح جميع المفضلة',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        content: const Text(
          'هل أنت متأكد من أنك تريد مسح جميع العناصر المفضلة؟ لا يمكن التراجع عن هذا الإجراء.',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              favoritesProvider.clearAllFavorites();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'تم مسح جميع المفضلة',
                    style: TextStyle(fontFamily: 'Amiri'),
                  ),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'مسح الكل',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }
}
