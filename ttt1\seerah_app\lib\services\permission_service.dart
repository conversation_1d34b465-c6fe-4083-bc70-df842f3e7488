import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionService {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// طلب جميع الأذونات المطلوبة
  static Future<bool> requestAllPermissions() async {
    try {
      // طلب إذن التخزين أولاً (الأهم)
      final storageGranted = await requestStoragePermission();
      if (!storageGranted) {
        debugPrint('❌ Storage permission denied - app cannot function');
        return false;
      }

      // طلب باقي الأذونات
      await Future.wait([
        requestNotificationPermission(),
        requestBatteryOptimizationPermission(),
        requestScheduleExactAlarmPermission(),
      ]);

      return true;
    } catch (e) {
      debugPrint('💥 Error requesting permissions: $e');
      return false;
    }
  }

  /// طلب إذن التخزين (الأهم)
  static Future<bool> requestStoragePermission() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        // Android 13+ (API 33+) - طلب أذونات الوسائط الجديدة
        final statuses = await [
          Permission.photos,
          Permission.videos,
          Permission.audio,
        ].request();

        final allGranted = statuses.values.every((status) => status.isGranted);
        debugPrint('📁 Media permissions (Android 13+): $allGranted');
        return allGranted;
      } else if (sdkInt >= 30) {
        // Android 11-12 - طلب MANAGE_EXTERNAL_STORAGE
        final status = await Permission.manageExternalStorage.request();
        final granted = status.isGranted;
        debugPrint('📁 MANAGE_EXTERNAL_STORAGE: $granted');
        return granted;
      } else {
        // Android 10 وأقل - طلب READ/WRITE_EXTERNAL_STORAGE
        final storageStatus = await Permission.storage.request();
        debugPrint('📁 Storage permission status: $storageStatus');

        // للتوافق مع Android 9، نتحقق من إذن التخزين الأساسي فقط
        final granted = storageStatus.isGranted;
        debugPrint('📁 Storage permissions (Android 9-10): $granted');
        return granted;
      }
    } catch (e) {
      debugPrint('💥 Error requesting storage permission: $e');
      return false;
    }
  }

  /// طلب إذن الإشعارات
  static Future<bool> requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      final granted = status.isGranted;
      debugPrint('🔔 Notification permission: $granted');
      return granted;
    } catch (e) {
      debugPrint('💥 Error requesting notification permission: $e');
      return false;
    }
  }

  /// طلب إذن تجاهل تحسين البطارية
  static Future<bool> requestBatteryOptimizationPermission() async {
    try {
      final status = await Permission.ignoreBatteryOptimizations.request();
      final granted = status.isGranted;
      debugPrint('🔋 Battery optimization permission: $granted');
      return granted;
    } catch (e) {
      debugPrint('💥 Error requesting battery optimization permission: $e');
      return false;
    }
  }

  /// طلب إذن جدولة المنبهات الدقيقة (Android 12+)
  static Future<bool> requestScheduleExactAlarmPermission() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 31) {
        final status = await Permission.scheduleExactAlarm.request();
        final granted = status.isGranted;
        debugPrint('⏰ Schedule exact alarm permission: $granted');
        return granted;
      } else {
        // غير مطلوب في الإصدارات الأقدم
        return true;
      }
    } catch (e) {
      debugPrint('💥 Error requesting schedule exact alarm permission: $e');
      return false;
    }
  }

  /// فحص إذا كانت جميع الأذونات ممنوحة
  static Future<bool> areAllPermissionsGranted() async {
    try {
      final storageGranted = await isStoragePermissionGranted();
      final notificationGranted = await isNotificationPermissionGranted();

      return storageGranted && notificationGranted;
    } catch (e) {
      debugPrint('💥 Error checking permissions: $e');
      return false;
    }
  }

  /// فحص إذن التخزين
  static Future<bool> isStoragePermissionGranted() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        // Android 13+ - فحص أذونات الوسائط
        final photosGranted = await Permission.photos.isGranted;
        final videosGranted = await Permission.videos.isGranted;
        return photosGranted && videosGranted;
      } else if (sdkInt >= 30) {
        // Android 11-12 - فحص MANAGE_EXTERNAL_STORAGE
        return await Permission.manageExternalStorage.isGranted;
      } else {
        // Android 10 وأقل - فحص إذن التخزين الأساسي فقط
        final storageGranted = await Permission.storage.isGranted;
        debugPrint('📁 Storage permission check (Android 9-10): $storageGranted');
        return storageGranted;
      }
    } catch (e) {
      debugPrint('💥 Error checking storage permission: $e');
      return false;
    }
  }

  /// فحص إذن الإشعارات
  static Future<bool> isNotificationPermissionGranted() async {
    try {
      return await Permission.notification.isGranted;
    } catch (e) {
      debugPrint('💥 Error checking notification permission: $e');
      return false;
    }
  }

  /// فحص إذن تجاهل تحسين البطارية
  static Future<bool> isBatteryOptimizationPermissionGranted() async {
    try {
      return await Permission.ignoreBatteryOptimizations.isGranted;
    } catch (e) {
      debugPrint('💥 Error checking battery optimization permission: $e');
      return false;
    }
  }

  /// فحص إذن جدولة المنبهات الدقيقة
  static Future<bool> isScheduleExactAlarmPermissionGranted() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 31) {
        return await Permission.scheduleExactAlarm.isGranted;
      } else {
        return true; // غير مطلوب في الإصدارات الأقدم
      }
    } catch (e) {
      debugPrint('💥 Error checking schedule exact alarm permission: $e');
      return false;
    }
  }

  /// فتح إعدادات التطبيق
  static Future<void> openAppSettings() async {
    try {
      await Permission.storage.request();
    } catch (e) {
      debugPrint('💥 Error opening app settings: $e');
    }
  }

  /// الحصول على حالة جميع الأذونات
  static Future<Map<String, bool>> getAllPermissionsStatus() async {
    return {
      'storage': await isStoragePermissionGranted(),
      'notification': await isNotificationPermissionGranted(),
      'battery_optimization': await isBatteryOptimizationPermissionGranted(),
      'schedule_exact_alarm': await isScheduleExactAlarmPermissionGranted(),
    };
  }

  /// فحص إصدار Android
  static Future<int> getAndroidSdkVersion() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      return androidInfo.version.sdkInt;
    } catch (e) {
      debugPrint('💥 Error getting Android SDK version: $e');
      return 0;
    }
  }

  /// فحص إذا كان الجهاز يدعم الخدمات الخلفية
  static Future<bool> supportsBackgroundServices() async {
    try {
      final sdkVersion = await getAndroidSdkVersion();
      // الخدمات الخلفية مدعومة في جميع الإصدارات، لكن مع قيود في الأحدث
      return sdkVersion >= 21; // Android 5.0+
    } catch (e) {
      debugPrint('💥 Error checking background services support: $e');
      return false;
    }
  }

  /// طلب الأذونات الحرجة فقط (للتطبيق الخاص)
  static Future<bool> requestCriticalPermissions() async {
    try {
      debugPrint('🔍 Starting critical permissions request...');

      // طلب إذن التخزين فقط (الأهم)
      final storageGranted = await requestStoragePermission();
      debugPrint('📁 Storage permission result: $storageGranted');

      if (storageGranted) {
        // طلب الإشعارات بصمت (بدون إجبار)
        await requestNotificationPermission();
        debugPrint('✅ Critical permissions granted successfully');
        return true;
      }

      debugPrint('❌ Storage permission denied');
      return false;
    } catch (e) {
      debugPrint('💥 Error requesting critical permissions: $e');
      return false;
    }
  }
}
