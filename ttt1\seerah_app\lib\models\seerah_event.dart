class SeerahEvent {
  final String id;
  final String title;
  final String description;
  final String detailedDescription;
  final String category;
  final String period;
  final String ageOfProphet;
  final String location;
  final String islamicYear;
  final String christianYear;
  final String significance;
  final String lessons;
  final String relatedVerses;
  final String relatedHadiths;
  final int order;
  final int importance;
  final String emoji;

  SeerahEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.detailedDescription,
    required this.category,
    required this.period,
    required this.ageOfProphet,
    required this.location,
    required this.islamicYear,
    required this.christianYear,
    required this.significance,
    required this.lessons,
    required this.relatedVerses,
    required this.relatedHadiths,
    required this.order,
    required this.importance,
    required this.emoji,
  });

  factory SeerahEvent.fromJson(Map<String, dynamic> json) {
    return SeerahEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      detailedDescription: json['detailedDescription'] ?? '',
      category: json['category'] ?? '',
      period: json['period'] ?? '',
      ageOfProphet: json['ageOfProphet'] ?? '',
      location: json['location'] ?? '',
      islamicYear: json['islamicYear'] ?? '',
      christianYear: json['year'] ?? '',
      significance: json['significance'] ?? '',
      lessons: json['lessons'] ?? '',
      relatedVerses: json['relatedVerses'] ?? '',
      relatedHadiths: json['relatedHadiths'] ?? '',
      order: json['order'] ?? 0,
      importance: json['importance'] ?? 1,
      emoji: json['emoji'] ?? '📖',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'detailedDescription': detailedDescription,
      'category': category,
      'period': period,
      'ageOfProphet': ageOfProphet,
      'location': location,
      'islamicYear': islamicYear,
      'year': christianYear,
      'significance': significance,
      'lessons': lessons,
      'relatedVerses': relatedVerses,
      'relatedHadiths': relatedHadiths,
      'order': order,
      'importance': importance,
      'emoji': emoji,
    };
  }
}
