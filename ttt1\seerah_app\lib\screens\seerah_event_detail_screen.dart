import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/seerah_event.dart';
import '../providers/favorites_provider.dart';

class SeerahEventDetailScreen extends StatefulWidget {
  final SeerahEvent event;

  const SeerahEventDetailScreen({
    super.key,
    required this.event,
  });

  @override
  State<SeerahEventDetailScreen> createState() => _SeerahEventDetailScreenState();
}

class _SeerahEventDetailScreenState extends State<SeerahEventDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Color _getCategoryColor() {
    switch (widget.event.category) {
      case 'ما قبل البعثة':
        return const Color(0xFF8BC34A);
      case 'بداية الوحي والدعوة السرية':
        return const Color(0xFF2196F3);
      case 'الدعوة الجهرية والهجرة':
        return const Color(0xFFFF9800);
      case 'العهد المدني والغزوات':
        return const Color(0xFFF44336);
      default:
        return const Color(0xFF9C27B0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            _buildSliverAppBar(),
            SliverToBoxAdapter(
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: _getCategoryColor(),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            final isFavorite = favoritesProvider.isFavorite(
              widget.event.id,
              FavoriteType.seerah,
            );
            return IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: Colors.white,
              ),
              onPressed: () => _toggleFavorite(favoritesProvider, isFavorite),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: _shareEvent,
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _getCategoryColor(),
                _getCategoryColor().withValues(alpha: 0.8),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        widget.event.emoji,
                        style: const TextStyle(fontSize: 40),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الحدث رقم ${widget.event.order}',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize: 14,
                                fontFamily: 'Amiri',
                              ),
                            ),
                            Text(
                              widget.event.title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Amiri',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildImportanceStars(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImportanceStars() {
    return Row(
      children: [
        const Text(
          'الأهمية: ',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 14,
            fontFamily: 'Amiri',
          ),
        ),
        ...List.generate(5, (index) {
          return Icon(
            index < widget.event.importance ? Icons.star : Icons.star_border,
            size: 20,
            color: index < widget.event.importance
                ? Colors.amber
                : Colors.white54,
          );
        }),
      ],
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCards(),
          const SizedBox(height: 24),
          _buildDescriptionSection(),
          const SizedBox(height: 24),
          _buildDetailedDescriptionSection(),
          const SizedBox(height: 24),
          _buildLessonsSection(),
          const SizedBox(height: 24),
          _buildRelatedContentSection(),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildInfoCards() {
    return Row(
      children: [
        Expanded(child: _buildInfoCard('التاريخ الميلادي', widget.event.christianYear, Icons.calendar_today)),
        const SizedBox(width: 12),
        Expanded(child: _buildInfoCard('التاريخ الهجري', widget.event.islamicYear, Icons.calendar_month)),
      ],
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: _getCategoryColor(), size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyLarge?.color,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return _buildSection(
      'نبذة مختصرة',
      widget.event.description,
      Icons.description,
    );
  }

  Widget _buildDetailedDescriptionSection() {
    return _buildSection(
      'التفاصيل الكاملة',
      widget.event.detailedDescription,
      Icons.article,
    );
  }

  Widget _buildLessonsSection() {
    return _buildSection(
      'الدروس المستفادة',
      widget.event.lessons,
      Icons.school,
    );
  }

  Widget _buildSection(String title, String content, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: _getCategoryColor(), size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getCategoryColor(),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).textTheme.bodyMedium?.color,
              height: 1.8,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRelatedContentSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor().withValues(alpha: 0.1),
            _getCategoryColor().withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getCategoryColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.link, color: _getCategoryColor(), size: 24),
              const SizedBox(width: 12),
              Text(
                'معلومات إضافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _getCategoryColor(),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('المكان', widget.event.location, Icons.location_on),
          _buildInfoRow('عمر النبي ﷺ', widget.event.ageOfProphet, Icons.person),
          _buildInfoRow('الفترة', widget.event.period, Icons.timeline),
          _buildInfoRow('الأهمية التاريخية', widget.event.significance, Icons.star),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 18, color: _getCategoryColor()),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: _getCategoryColor(),
                    fontFamily: 'Amiri',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                    fontFamily: 'Amiri',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _toggleFavorite(FavoritesProvider favoritesProvider, bool isFavorite) {
    favoritesProvider.toggleFavorite(widget.event.id, FavoriteType.seerah);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isFavorite ? 'تم إزالة الحدث من المفضلة' : 'تم إضافة الحدث للمفضلة',
          style: const TextStyle(fontFamily: 'Amiri'),
        ),
        backgroundColor: isFavorite ? Colors.orange : Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareEvent() {
    final text = '''
${widget.event.title}

${widget.event.description}

التاريخ: ${widget.event.christianYear}
المكان: ${widget.event.location}
عمر النبي ﷺ: ${widget.event.ageOfProphet}

من تطبيق السيرة النبوية
''';

    Clipboard.setData(ClipboardData(text: text));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'تم نسخ تفاصيل الحدث',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        backgroundColor: _getCategoryColor(),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
