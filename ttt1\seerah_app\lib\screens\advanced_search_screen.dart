import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/search_provider.dart';

class AdvancedSearchScreen extends StatefulWidget {
  const AdvancedSearchScreen({super.key});

  @override
  State<AdvancedSearchScreen> createState() => _AdvancedSearchScreenState();
}

class _AdvancedSearchScreenState extends State<AdvancedSearchScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text(
            'البحث المتقدم',
            style: TextStyle(
              fontFamily: 'Amiri',
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: Consumer<SearchProvider>(
          builder: (context, searchProvider, child) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height -
                      AppBar().preferredSize.height -
                      MediaQuery.of(context).padding.top,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      _buildSearchHeader(searchProvider),
                      _buildSearchField(searchProvider),
                      if (searchProvider.query.isNotEmpty) _buildFilters(searchProvider),
                      Expanded(
                        child: _buildSearchResults(searchProvider),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
    );
  }

  Widget _buildSearchHeader(SearchProvider searchProvider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          const Text(
            '🔍',
            style: TextStyle(fontSize: 40),
          ),
          const SizedBox(height: 8),
          const Text(
            'البحث المتقدم',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            searchProvider.results.isEmpty && searchProvider.query.isEmpty
                ? 'ابحث في جميع محتويات التطبيق'
                : 'تم العثور على ${searchProvider.results.length} نتيجة',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(SearchProvider searchProvider) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'البحث في المحتوى',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontFamily: 'Amiri',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث في السيرة والأحاديث والصحابة...',
              hintStyle: const TextStyle(fontFamily: 'Amiri'),
              border: const OutlineInputBorder(),
              prefixIcon: const Icon(Icons.search),
              suffixIcon: searchProvider.isSearching
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: EdgeInsets.all(12),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : _searchController.text.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            searchProvider.clearResults();
                          },
                          icon: const Icon(Icons.clear),
                        )
                      : null,
            ),
            style: const TextStyle(fontFamily: 'Amiri'),
            onChanged: (value) {
              if (value.trim().isNotEmpty) {
                searchProvider.search(value);
              } else {
                searchProvider.clearResults();
              }
            },
            onSubmitted: (value) {
              if (value.trim().isNotEmpty) {
                searchProvider.search(value);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilters(SearchProvider searchProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // Category filters
          Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: SearchCategory.values.map((category) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(
                            _getCategoryName(category),
                            style: const TextStyle(fontFamily: 'Amiri'),
                          ),
                          selected: searchProvider.category == category,
                          onSelected: (selected) {
                            if (selected) {
                              searchProvider.setCategory(category);
                            }
                          },
                          selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                          checkmarkColor: Theme.of(context).colorScheme.primary,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
              // Sort button
              PopupMenuButton<SortBy>(
                icon: const Icon(Icons.sort),
                tooltip: 'ترتيب النتائج',
                onSelected: (sortBy) => searchProvider.setSortBy(sortBy),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: SortBy.relevance,
                    child: Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: searchProvider.sortBy == SortBy.relevance
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'الأكثر صلة',
                          style: TextStyle(
                            fontFamily: 'Amiri',
                            fontWeight: searchProvider.sortBy == SortBy.relevance
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: searchProvider.sortBy == SortBy.relevance
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: SortBy.alphabetical,
                    child: Row(
                      children: [
                        Icon(
                          Icons.sort_by_alpha,
                          color: searchProvider.sortBy == SortBy.alphabetical
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'أبجدي',
                          style: TextStyle(
                            fontFamily: 'Amiri',
                            fontWeight: searchProvider.sortBy == SortBy.alphabetical
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: searchProvider.sortBy == SortBy.alphabetical
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Advanced filters button
              IconButton(
                icon: const Icon(Icons.tune),
                tooltip: 'فلاتر متقدمة',
                onPressed: () => _showAdvancedFiltersDialog(searchProvider),
              ),
            ],
          ),
          // Quick filter chips for active filters
          if (searchProvider.hasActiveFilters) ...[
            const SizedBox(height: 8),
            _buildActiveFilters(searchProvider),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchResults(SearchProvider searchProvider) {
    if (searchProvider.query.isEmpty) {
      return _buildEmptyState();
    }

    if (searchProvider.isSearching) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (searchProvider.results.isEmpty) {
      return _buildNoResults();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: searchProvider.results.length,
      itemBuilder: (context, index) {
        final result = searchProvider.results[index];
        return _buildResultCard(result);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'ابدأ البحث',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اكتب في حقل البحث للعثور على المحتوى',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نتائج',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب كلمات مختلفة أو تحقق من الإملاء',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResultCard(SearchResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Text(
          result.title,
          style: const TextStyle(
            fontFamily: 'Amiri',
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              result.content,
              style: const TextStyle(fontFamily: 'Amiri'),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    result.category,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.primary,
                      fontFamily: 'Amiri',
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  'الصلة: ${(result.relevanceScore / 10).toStringAsFixed(1)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontFamily: 'Amiri',
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _navigateToDetail(result),
      ),
    );
  }

  String _getCategoryName(SearchCategory category) {
    switch (category) {
      case SearchCategory.all:
        return 'الكل';
      case SearchCategory.seerah:
        return 'السيرة';
      case SearchCategory.hadith:
        return 'الأحاديث';
      case SearchCategory.companions:
        return 'الصحابة';
    }
  }

  void _navigateToDetail(SearchResult result) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'فتح تفاصيل: ${result.title}',
          style: const TextStyle(fontFamily: 'Amiri'),
        ),
      ),
    );
  }

  Widget _buildActiveFilters(SearchProvider searchProvider) {
    List<Widget> activeFilters = [];

    // Add category filter if not 'all'
    if (searchProvider.category != SearchCategory.all) {
      activeFilters.add(
        Chip(
          label: Text(
            _getCategoryName(searchProvider.category),
            style: const TextStyle(fontFamily: 'Amiri', fontSize: 12),
          ),
          deleteIcon: const Icon(Icons.close, size: 16),
          onDeleted: () => searchProvider.setCategory(SearchCategory.all),
          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        ),
      );
    }

    // Add sort filter if not relevance
    if (searchProvider.sortBy != SortBy.relevance) {
      activeFilters.add(
        Chip(
          label: Text(
            'ترتيب: ${searchProvider.sortBy == SortBy.alphabetical ? "أبجدي" : "الصلة"}',
            style: const TextStyle(fontFamily: 'Amiri', fontSize: 12),
          ),
          deleteIcon: const Icon(Icons.close, size: 16),
          onDeleted: () => searchProvider.setSortBy(SortBy.relevance),
          backgroundColor: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
        ),
      );
    }

    if (activeFilters.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      width: double.infinity,
      child: Wrap(
        spacing: 8,
        runSpacing: 4,
        children: [
          Text(
            'الفلاتر النشطة:',
            style: TextStyle(
              fontFamily: 'Amiri',
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          ...activeFilters,
          if (activeFilters.length > 1)
            TextButton.icon(
              onPressed: () {
                searchProvider.setCategory(SearchCategory.all);
                searchProvider.setSortBy(SortBy.relevance);
              },
              icon: const Icon(Icons.clear_all, size: 16),
              label: const Text(
                'مسح الكل',
                style: TextStyle(fontFamily: 'Amiri', fontSize: 12),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
        ],
      ),
    );
  }

  void _showAdvancedFiltersDialog(SearchProvider searchProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'الفلاتر المتقدمة',
          style: TextStyle(fontFamily: 'Amiri', fontWeight: FontWeight.bold),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category section
              const Text(
                'الفئة:',
                style: TextStyle(
                  fontFamily: 'Amiri',
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: SearchCategory.values.map((category) {
                  return FilterChip(
                    label: Text(
                      _getCategoryName(category),
                      style: const TextStyle(fontFamily: 'Amiri'),
                    ),
                    selected: searchProvider.category == category,
                    onSelected: (selected) {
                      if (selected) {
                        searchProvider.setCategory(category);
                      }
                    },
                    selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    checkmarkColor: Theme.of(context).colorScheme.primary,
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Sort section
              const Text(
                'ترتيب النتائج:',
                style: TextStyle(
                  fontFamily: 'Amiri',
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Column(
                children: SortBy.values.map((sortBy) {
                  return RadioListTile<SortBy>(
                    title: Text(
                      sortBy == SortBy.relevance ? 'الأكثر صلة' : 'أبجدي',
                      style: const TextStyle(fontFamily: 'Amiri'),
                    ),
                    subtitle: Text(
                      sortBy == SortBy.relevance
                          ? 'ترتيب حسب مدى الصلة بالبحث'
                          : 'ترتيب أبجدي حسب العنوان',
                      style: const TextStyle(fontFamily: 'Amiri', fontSize: 12),
                    ),
                    value: sortBy,
                    groupValue: searchProvider.sortBy,
                    onChanged: (value) {
                      if (value != null) {
                        searchProvider.setSortBy(value);
                      }
                    },
                    dense: true,
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Reset filters
              searchProvider.setCategory(SearchCategory.all);
              searchProvider.setSortBy(SortBy.relevance);
            },
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'تطبيق',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }


}
