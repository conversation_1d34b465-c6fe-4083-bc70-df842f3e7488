import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/hadith.dart';
import '../providers/favorites_provider.dart';

class HadithCard extends StatefulWidget {
  final Hadith hadith;
  final VoidCallback? onTap;
  final bool showFullText;

  const HadithCard({
    super.key,
    required this.hadith,
    this.onTap,
    this.showFullText = false,
  });

  @override
  State<HadithCard> createState() => _HadithCardState();
}

class _HadithCardState extends State<HadithCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getCategoryColor() {
    switch (widget.hadith.category) {
      case 'أحاديث العقيدة':
        return const Color(0xFF4CAF50);
      case 'أحاديث العبادة':
        return const Color(0xFF2196F3);
      case 'أحاديث الأخلاق':
        return const Color(0xFFFF9800);
      case 'أحاديث المعاملات':
        return const Color(0xFF9C27B0);
      case 'أحاديث الآداب الاجتماعية':
        return const Color(0xFFF44336);
      case 'أحاديث الدعاء والذكر':
        return const Color(0xFF607D8B);
      default:
        return const Color(0xFF795548);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
        if (widget.onTap != null) {
          Future.delayed(const Duration(milliseconds: 100), widget.onTap!);
        }
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: _getCategoryColor().withValues(alpha: 0.2),
                    blurRadius: _isPressed ? 8 : 12,
                    offset: Offset(0, _isPressed ? 2 : 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(),
                    _buildArabicText(),
                    _buildTranslation(),
                    _buildExplanationPreview(),
                    _buildFooter(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor(),
            _getCategoryColor().withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'حديث ${widget.hadith.number}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
                fontFamily: 'Amiri',
              ),
            ),
          ),
          const Spacer(),
          Consumer<FavoritesProvider>(
            builder: (context, favoritesProvider, child) {
              final isFavorite = favoritesProvider.isFavorite(
                widget.hadith.id,
                FavoriteType.hadith,
              );
              return IconButton(
                onPressed: () {
                  favoritesProvider.toggleFavorite(
                    widget.hadith.id,
                    FavoriteType.hadith,
                  );
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isFavorite
                            ? 'تم إزالة الحديث من المفضلة'
                            : 'تم إضافة الحديث للمفضلة',
                        style: const TextStyle(fontFamily: 'Amiri'),
                      ),
                      backgroundColor: isFavorite ? Colors.orange : Colors.green,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: Colors.white,
                  size: 20,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              );
            },
          ),
          const SizedBox(width: 8),
          Text(
            widget.hadith.themeEmoji,
            style: const TextStyle(fontSize: 24),
          ),
        ],
      ),
    );
  }

  Widget _buildArabicText() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).colorScheme.surface.withValues(alpha: 0.5)
            : Colors.grey[50],
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)
                : Colors.grey[200]!,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            widget.showFullText
                ? widget.hadith.arabicText
                : _truncateText(widget.hadith.arabicText, 80),
            style: TextStyle(
              fontSize: 18,
              color: Theme.of(context).textTheme.bodyLarge?.color,
              height: 1.8,
              fontFamily: 'Amiri',
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Icon(
                Icons.format_quote,
                size: 16,
                color: _getCategoryColor(),
              ),
              const SizedBox(width: 4),
              Text(
                'النص العربي',
                style: TextStyle(
                  fontSize: 12,
                  color: _getCategoryColor(),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTranslation() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.translate,
                size: 16,
                color: _getCategoryColor(),
              ),
              const SizedBox(width: 8),
              Text(
                'المعنى',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _getCategoryColor(),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.showFullText
                ? widget.hadith.translation
                : _truncateText(widget.hadith.translation, 100),
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).textTheme.bodyMedium?.color,
              height: 1.6,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExplanationPreview() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 16,
                color: _getCategoryColor(),
              ),
              const SizedBox(width: 8),
              Text(
                'الشرح',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _getCategoryColor(),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _truncateText(widget.hadith.explanation, 120),
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              height: 1.5,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Theme.of(context).colorScheme.surface.withValues(alpha: 0.5)
            : Colors.grey[50],
        border: Border(
          top: BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)
                : Colors.grey[200]!,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                size: 16,
                color: _getCategoryColor(),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'الراوي: ${widget.hadith.narrator}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                    fontFamily: 'Amiri',
                  ),
                ),
              ),
              Icon(
                Icons.book,
                size: 16,
                color: _getCategoryColor(),
              ),
              const SizedBox(width: 8),
              Text(
                widget.hadith.source,
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                  fontFamily: 'Amiri',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  widget.hadith.theme,
                  style: TextStyle(
                    color: _getCategoryColor(),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Amiri',
                  ),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  widget.hadith.category,
                  style: TextStyle(
                    color: _getCategoryColor(),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    fontFamily: 'Amiri',
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }
}
