// اختبار شامل لوظائف المفضلة
// هذا الملف للاختبار اليدوي والتحقق من الوظائف

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/favorites_provider.dart';
import 'lib/data/seerah_data.dart';
import 'lib/data/hadith_data.dart';
import 'lib/data/companions_data.dart';

void main() {
  runApp(TestApp());
}

class TestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => FavoritesProvider(),
      child: MaterialApp(
        title: 'اختبار المفضلة',
        home: TestScreen(),
      ),
    );
  }
}

class TestScreen extends StatefulWidget {
  @override
  _TestScreenState createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  @override
  void initState() {
    super.initState();
    // تهيئة المفضلة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FavoritesProvider>().initializeFavorites();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('اختبار وظائف المفضلة'),
        backgroundColor: Colors.blue,
      ),
      body: Consumer<FavoritesProvider>(
        builder: (context, favoritesProvider, child) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات المفضلة
                _buildFavoritesInfo(favoritesProvider),
                SizedBox(height: 20),
                
                // اختبار إضافة السيرة
                _buildTestSection(
                  'اختبار إضافة أحداث السيرة',
                  () => _testSeerahFavorites(favoritesProvider),
                ),
                SizedBox(height: 20),
                
                // اختبار إضافة الأحاديث
                _buildTestSection(
                  'اختبار إضافة الأحاديث',
                  () => _testHadithFavorites(favoritesProvider),
                ),
                SizedBox(height: 20),
                
                // اختبار إضافة الصحابة
                _buildTestSection(
                  'اختبار إضافة الصحابة',
                  () => _testCompanionFavorites(favoritesProvider),
                ),
                SizedBox(height: 20),
                
                // عرض المفضلة الحالية
                _buildCurrentFavorites(favoritesProvider),
                SizedBox(height: 20),
                
                // أزرار التحكم
                _buildControlButtons(favoritesProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFavoritesInfo(FavoritesProvider provider) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات المفضلة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('إجمالي المفضلة: ${provider.favorites.length}'),
            Text('السيرة: ${provider.getFavoritesCountByType(FavoriteType.seerah)}'),
            Text('الأحاديث: ${provider.getFavoritesCountByType(FavoriteType.hadith)}'),
            Text('الصحابة: ${provider.getFavoritesCountByType(FavoriteType.companion)}'),
            if (provider.error != null)
              Text('خطأ: ${provider.error}', style: TextStyle(color: Colors.red)),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection(String title, VoidCallback onTest) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: onTest,
              child: Text('تشغيل الاختبار'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentFavorites(FavoritesProvider provider) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المفضلة الحالية',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            if (provider.favorites.isEmpty)
              Text('لا توجد مفضلة')
            else
              ...provider.favorites.map((favorite) => ListTile(
                title: Text(favorite.title),
                subtitle: Text('${favorite.type.toString()} - ${favorite.subtitle}'),
                trailing: IconButton(
                  icon: Icon(Icons.delete, color: Colors.red),
                  onPressed: () => provider.removeFromFavorites(favorite.id, favorite.type),
                ),
              )).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons(FavoritesProvider provider) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () => provider.clearAllFavorites(),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('مسح الكل', style: TextStyle(color: Colors.white)),
          ),
        ),
        SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: () => provider.initializeFavorites(),
            child: Text('إعادة تحميل'),
          ),
        ),
      ],
    );
  }

  void _testSeerahFavorites(FavoritesProvider provider) {
    try {
      final events = SeerahData.getAllEvents();
      if (events.isNotEmpty) {
        final firstEvent = events.first;
        provider.addToFavorites(firstEvent.id, FavoriteType.seerah);
        _showMessage('تم إضافة حدث السيرة: ${firstEvent.title}');
      } else {
        _showMessage('لا توجد أحداث سيرة للاختبار', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في اختبار السيرة: $e', isError: true);
    }
  }

  void _testHadithFavorites(FavoritesProvider provider) {
    try {
      final hadiths = HadithData.getAllHadiths();
      if (hadiths.isNotEmpty) {
        final firstHadith = hadiths.first;
        provider.addToFavorites(firstHadith.id, FavoriteType.hadith);
        _showMessage('تم إضافة حديث: ${firstHadith.theme}');
      } else {
        _showMessage('لا توجد أحاديث للاختبار', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في اختبار الأحاديث: $e', isError: true);
    }
  }

  void _testCompanionFavorites(FavoritesProvider provider) {
    try {
      final companions = CompanionsData.getAllCompanions();
      if (companions.isNotEmpty) {
        final firstCompanion = companions.first;
        provider.addToFavorites(firstCompanion.id, FavoriteType.companion);
        _showMessage('تم إضافة صحابي: ${firstCompanion.name}');
      } else {
        _showMessage('لا توجد صحابة للاختبار', isError: true);
      }
    } catch (e) {
      _showMessage('خطأ في اختبار الصحابة: $e', isError: true);
    }
  }

  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }
}
