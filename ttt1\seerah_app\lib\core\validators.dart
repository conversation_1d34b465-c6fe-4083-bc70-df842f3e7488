import '../models/companion.dart';
import '../models/hadith.dart';
import '../models/seerah_event.dart';

class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
}

class Validators {
  // Text validation
  static ValidationResult validateText(
    String? text, {
    String fieldName = 'النص',
    int minLength = 1,
    int maxLength = 1000,
    bool required = true,
    bool allowArabicOnly = false,
  }) {
    final errors = <String>[];
    final warnings = <String>[];

    if (text == null || text.trim().isEmpty) {
      if (required) {
        errors.add('$fieldName مطلوب');
      }
      return ValidationResult(isValid: !required, errors: errors);
    }

    final trimmedText = text.trim();

    // Length validation
    if (trimmedText.length < minLength) {
      errors.add('$fieldName يجب أن يكون على الأقل $minLength حرف');
    }

    if (trimmedText.length > maxLength) {
      errors.add('$fieldName يجب أن يكون أقل من $maxLength حرف');
    }

    // Arabic text validation
    if (allowArabicOnly) {
      final arabicRegex = RegExp(r'^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\p{P}]+$', unicode: true);
      if (!arabicRegex.hasMatch(trimmedText)) {
        warnings.add('$fieldName يفضل أن يكون باللغة العربية');
      }
    }

    // Check for suspicious content
    if (trimmedText.contains(RegExp(r'[<>{}]'))) {
      warnings.add('$fieldName يحتوي على رموز مشبوهة');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  // ID validation
  static ValidationResult validateId(String? id, {String fieldName = 'المعرف'}) {
    final errors = <String>[];

    if (id == null || id.trim().isEmpty) {
      errors.add('$fieldName مطلوب');
      return ValidationResult(isValid: false, errors: errors);
    }

    final trimmedId = id.trim();

    // ID format validation
    if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(trimmedId)) {
      errors.add('$fieldName يجب أن يحتوي على أحرف وأرقام فقط');
    }

    if (trimmedId.length < 3) {
      errors.add('$fieldName يجب أن يكون على الأقل 3 أحرف');
    }

    if (trimmedId.length > 50) {
      errors.add('$fieldName يجب أن يكون أقل من 50 حرف');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  // Date validation
  static ValidationResult validateDate(String? date, {String fieldName = 'التاريخ'}) {
    final errors = <String>[];
    final warnings = <String>[];

    if (date == null || date.trim().isEmpty) {
      errors.add('$fieldName مطلوب');
      return ValidationResult(isValid: false, errors: errors);
    }

    final trimmedDate = date.trim();

    // Basic date format validation
    if (trimmedDate.length < 4) {
      errors.add('$fieldName غير صحيح');
    }

    // Check for common date patterns
    final datePatterns = [
      RegExp(r'^\d{1,4}\s*(م|هـ)$'), // Year with era
      RegExp(r'^\d{1,2}/\d{1,4}$'), // Month/Year
      RegExp(r'^\d{1,2}/\d{1,2}/\d{1,4}$'), // Day/Month/Year
      RegExp(r'^السنة\s+\d+'), // Arabic year format
    ];

    bool isValidFormat = datePatterns.any((pattern) => pattern.hasMatch(trimmedDate));

    if (!isValidFormat && !trimmedDate.contains(RegExp(r'[أ-ي]'))) {
      warnings.add('$fieldName قد يكون بصيغة غير مألوفة');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  // Category validation
  static ValidationResult validateCategory(String? category) {
    final errors = <String>[];

    if (category == null || category.trim().isEmpty) {
      errors.add('الفئة مطلوبة');
      return ValidationResult(isValid: false, errors: errors);
    }

    final validCategories = [
      'العشرة المبشرون بالجنة',
      'أمهات المؤمنين',
      'الأنصار',
      'المهاجرون',
      'الصحابيات',
      'التابعون',
    ];

    if (!validCategories.contains(category.trim())) {
      errors.add('فئة غير صحيحة: $category');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  // Companion validation
  static ValidationResult validateCompanion(Companion companion) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate ID
    final idResult = validateId(companion.id, fieldName: 'معرف الصحابي');
    errors.addAll(idResult.errors);

    // Validate name
    final nameResult = validateText(
      companion.name,
      fieldName: 'اسم الصحابي',
      minLength: 2,
      maxLength: 100,
      allowArabicOnly: true,
    );
    errors.addAll(nameResult.errors);
    warnings.addAll(nameResult.warnings);

    // Validate full name
    final fullNameResult = validateText(
      companion.fullName,
      fieldName: 'الاسم الكامل',
      minLength: 5,
      maxLength: 200,
      allowArabicOnly: true,
    );
    errors.addAll(fullNameResult.errors);
    warnings.addAll(fullNameResult.warnings);

    // Validate biography
    final biographyResult = validateText(
      companion.biography,
      fieldName: 'السيرة المختصرة',
      minLength: 20,
      maxLength: 500,
      allowArabicOnly: true,
    );
    errors.addAll(biographyResult.errors);
    warnings.addAll(biographyResult.warnings);

    // Validate detailed biography
    final detailedBiographyResult = validateText(
      companion.detailedBiography,
      fieldName: 'السيرة المفصلة',
      minLength: 50,
      maxLength: 2000,
      allowArabicOnly: true,
    );
    errors.addAll(detailedBiographyResult.errors);
    warnings.addAll(detailedBiographyResult.warnings);

    // Validate category
    final categoryResult = validateCategory(companion.category);
    errors.addAll(categoryResult.errors);

    // Validate dates
    final birthYearResult = validateDate(companion.birthYear, fieldName: 'سنة الولادة');
    errors.addAll(birthYearResult.errors);
    warnings.addAll(birthYearResult.warnings);

    final deathYearResult = validateDate(companion.deathYear, fieldName: 'سنة الوفاة');
    errors.addAll(deathYearResult.errors);
    warnings.addAll(deathYearResult.warnings);

    // Validate places
    final birthPlaceResult = validateText(
      companion.birthPlace,
      fieldName: 'مكان الولادة',
      minLength: 2,
      maxLength: 100,
      allowArabicOnly: true,
    );
    errors.addAll(birthPlaceResult.errors);
    warnings.addAll(birthPlaceResult.warnings);

    // Cross-validation
    if (companion.name.isEmpty && companion.fullName.isNotEmpty) {
      warnings.add('الاسم المختصر فارغ بينما الاسم الكامل موجود');
    }

    if (companion.biography.length > companion.detailedBiography.length) {
      warnings.add('السيرة المختصرة أطول من السيرة المفصلة');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  // Hadith validation
  static ValidationResult validateHadith(Hadith hadith) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate ID
    final idResult = validateId(hadith.id, fieldName: 'معرف الحديث');
    errors.addAll(idResult.errors);

    // Validate Arabic text
    final arabicTextResult = validateText(
      hadith.arabicText,
      fieldName: 'النص العربي',
      minLength: 10,
      maxLength: 2000,
      allowArabicOnly: true,
    );
    errors.addAll(arabicTextResult.errors);
    warnings.addAll(arabicTextResult.warnings);

    // Validate theme (title)
    final titleResult = validateText(
      hadith.theme,
      fieldName: 'موضوع الحديث',
      minLength: 5,
      maxLength: 200,
      allowArabicOnly: true,
    );
    errors.addAll(titleResult.errors);
    warnings.addAll(titleResult.warnings);

    // Validate narrator
    final narratorResult = validateText(
      hadith.narrator,
      fieldName: 'الراوي',
      minLength: 3,
      maxLength: 100,
      allowArabicOnly: true,
    );
    errors.addAll(narratorResult.errors);
    warnings.addAll(narratorResult.warnings);

    // Validate source
    final sourceResult = validateText(
      hadith.source,
      fieldName: 'المصدر',
      minLength: 3,
      maxLength: 100,
      allowArabicOnly: true,
    );
    errors.addAll(sourceResult.errors);
    warnings.addAll(sourceResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  // Seerah event validation
  static ValidationResult validateSeerahEvent(SeerahEvent event) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validate ID
    final idResult = validateId(event.id, fieldName: 'معرف الحدث');
    errors.addAll(idResult.errors);

    // Validate title
    final titleResult = validateText(
      event.title,
      fieldName: 'عنوان الحدث',
      minLength: 5,
      maxLength: 200,
      allowArabicOnly: true,
    );
    errors.addAll(titleResult.errors);
    warnings.addAll(titleResult.warnings);

    // Validate description
    final descriptionResult = validateText(
      event.description,
      fieldName: 'وصف الحدث',
      minLength: 20,
      maxLength: 2000,
      allowArabicOnly: true,
    );
    errors.addAll(descriptionResult.errors);
    warnings.addAll(descriptionResult.warnings);

    // Validate date (using christianYear)
    final dateResult = validateDate(event.christianYear, fieldName: 'تاريخ الحدث');
    errors.addAll(dateResult.errors);
    warnings.addAll(dateResult.warnings);

    // Validate location
    final locationResult = validateText(
      event.location,
      fieldName: 'مكان الحدث',
      minLength: 2,
      maxLength: 100,
      allowArabicOnly: true,
    );
    errors.addAll(locationResult.errors);
    warnings.addAll(locationResult.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  // Batch validation
  static Map<String, ValidationResult> validateCompanions(List<Companion> companions) {
    final results = <String, ValidationResult>{};
    final usedIds = <String>{};

    for (final companion in companions) {
      final result = validateCompanion(companion);

      // Check for duplicate IDs
      if (usedIds.contains(companion.id)) {
        final errors = List<String>.from(result.errors);
        errors.add('معرف مكرر: ${companion.id}');
        results[companion.id] = ValidationResult(
          isValid: false,
          errors: errors,
          warnings: result.warnings,
        );
      } else {
        usedIds.add(companion.id);
        results[companion.id] = result;
      }
    }

    return results;
  }

  // Get validation summary
  static Map<String, dynamic> getValidationSummary(Map<String, ValidationResult> results) {
    int validCount = 0;
    int invalidCount = 0;
    int warningCount = 0;
    final allErrors = <String>[];
    final allWarnings = <String>[];

    for (final result in results.values) {
      if (result.isValid) {
        validCount++;
      } else {
        invalidCount++;
      }

      if (result.hasWarnings) {
        warningCount++;
      }

      allErrors.addAll(result.errors);
      allWarnings.addAll(result.warnings);
    }

    return {
      'total': results.length,
      'valid': validCount,
      'invalid': invalidCount,
      'withWarnings': warningCount,
      'totalErrors': allErrors.length,
      'totalWarnings': allWarnings.length,
      'errors': allErrors,
      'warnings': allWarnings,
    };
  }
}
