import 'package:flutter/material.dart';
import '../models/seerah_event.dart';
import '../data/seerah_data.dart';
import '../widgets/seerah_event_card.dart';
import '../widgets/category_filter_chip.dart';
import '../screens/seerah_event_detail_screen.dart';

class SeerahEventsScreen extends StatefulWidget {
  const SeerahEventsScreen({super.key});

  @override
  State<SeerahEventsScreen> createState() => _SeerahEventsScreenState();
}

class _SeerahEventsScreenState extends State<SeerahEventsScreen> {
  List<SeerahEvent> _allEvents = [];
  List<SeerahEvent> _filteredEvents = [];
  String _selectedCategory = 'الكل';
  String _searchQuery = '';
  bool _isLoading = true;

  final List<String> _categories = [
    'الكل',
    'ما قبل البعثة',
    'بداية الوحي والدعوة السرية',
    'الدعوة الجهرية والهجرة',
    'العهد المدني والغزوات',
  ];

  @override
  void initState() {
    super.initState();
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    setState(() => _isLoading = true);

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    _allEvents = SeerahData.getAllEvents();
    _filteredEvents = List.from(_allEvents);

    setState(() => _isLoading = false);
  }

  void _filterEvents() {
    setState(() {
      _filteredEvents = _allEvents.where((event) {
        final matchesCategory = _selectedCategory == 'الكل' ||
                               event.category == _selectedCategory;
        final matchesSearch = _searchQuery.isEmpty ||
                             event.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                             event.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                             event.location.toLowerCase().contains(_searchQuery.toLowerCase());
        return matchesCategory && matchesSearch;
      }).toList();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.primary,
      title: const Text(
        'أحداث السيرة النبوية',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Amiri',
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: Colors.white),
          onPressed: _showSearchDialog,
        ),
        IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white),
          onPressed: _showFilterDialog,
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل أحداث السيرة النبوية...',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        _buildStatsHeader(),
        _buildCategoryFilters(),
        Expanded(child: _buildEventsList()),
      ],
    );
  }

  Widget _buildStatsHeader() {
    return Container(
      margin: const EdgeInsets.all(12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('إجمالي الأحداث', '${_allEvents.length}', Icons.event),
          _buildStatItem('الأحداث المعروضة', '${_filteredEvents.length}', Icons.visibility),
          _buildStatItem('الفئات', '${_categories.length - 1}', Icons.category),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Amiri',
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontFamily: 'Amiri',
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilters() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: CategoryFilterChip(
              label: category,
              isSelected: _selectedCategory == category,
              onSelected: (selected) {
                if (selected) {
                  setState(() => _selectedCategory = category);
                  _filterEvents();
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildEventsList() {
    if (_filteredEvents.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredEvents.length,
      itemBuilder: (context, index) {
        final event = _filteredEvents[index];
        return SeerahEventCard(
          event: event,
          onTap: () => _navigateToEventDetail(event),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أحداث تطابق البحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو الفلترة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الأحداث', style: TextStyle(fontFamily: 'Amiri')),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'ابحث في العنوان أو الوصف أو المكان...',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) => _searchQuery = value,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              _filterEvents();
              Navigator.pop(context);
            },
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الأحداث', style: TextStyle(fontFamily: 'Amiri')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _categories.map((category) {
            return RadioListTile<String>(
              title: Text(category, style: const TextStyle(fontFamily: 'Amiri')),
              value: category,
              groupValue: _selectedCategory,
              onChanged: (value) {
                setState(() => _selectedCategory = value!);
                _filterEvents();
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _navigateToEventDetail(SeerahEvent event) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            SeerahEventDetailScreen(event: event),
        transitionDuration: const Duration(milliseconds: 200),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
      ),
    );
  }
}
