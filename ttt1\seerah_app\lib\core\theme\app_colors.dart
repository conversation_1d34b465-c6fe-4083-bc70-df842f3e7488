import 'package:flutter/material.dart';

class AppColors {
  // Primary Green Colors (من المواصفات)
  static const Color primaryGreen = Color(0xFF2E7D32);
  static const Color lightGreen = Color(0xFF4CAF50);
  static const Color darkGreen = Color(0xFF1B5E20);
  static const Color accentGreen = Color(0xFF66BB6A);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF2E7D32),
      Color(0xFF4CAF50),
      Color(0xFF66BB6A),
    ],
  );

  static const LinearGradient darkGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1B5E20),
      Color(0xFF2E7D32),
      Color(0xFF388E3C),
    ],
  );

  // Category Colors (من المواصفات)
  static const Color seerahColor = Color(0xFF2E7D32);      // أخضر داكن
  static const Color hadithColor = Color(0xFF1976D2);      // أزرق
  static const Color companionColor = Color(0xFF7B1FA2);   // بنفسجي
  static const Color searchColor = Color(0xFFFF6F00);      // برتقالي
  static const Color favoritesColor = Color(0xFFD32F2F);   // أحمر
  static const Color aboutColor = Color(0xFF455A64);       // رمادي مزرق

  // Light Theme Colors
  static const Color lightBackground = Color(0xFFF5F5F5);
  static const Color lightSurface = Colors.white;
  static const Color lightCardBackground = Colors.white;
  static const Color lightTextPrimary = Color(0xFF212121);
  static const Color lightTextSecondary = Color(0xFF757575);
  static const Color lightDivider = Color(0xFFE0E0E0);

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkCardBackground = Color(0xFF2D2D2D);
  static const Color darkTextPrimary = Color(0xFFE0E0E0);
  static const Color darkTextSecondary = Color(0xFFB0B0B0);
  static const Color darkDivider = Color(0xFF404040);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Islamic Colors
  static const Color goldAccent = Color(0xFFFFD700);
  static const Color islamicGreen = Color(0xFF00695C);
  static const Color prayerTimeColor = Color(0xFF006064);

  // Transparency Levels
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  // Helper Methods
  static Color getContrastColor(Color backgroundColor) {
    // Calculate luminance to determine if text should be light or dark
    double luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  static Color getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'seerah':
      case 'السيرة':
        return seerahColor;
      case 'hadith':
      case 'الأحاديث':
        return hadithColor;
      case 'companion':
      case 'الصحابة':
        return companionColor;
      case 'search':
      case 'البحث':
        return searchColor;
      case 'favorites':
      case 'المفضلة':
        return favoritesColor;
      case 'about':
      case 'حول':
        return aboutColor;
      default:
        return primaryGreen;
    }
  }
}
