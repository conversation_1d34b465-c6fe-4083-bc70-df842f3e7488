// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:seerah_app/main.dart';

void main() {
  testWidgets('Seerah App smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that our app starts with the correct title
    expect(find.text('السيرة النبوية الشريفة'), findsOneWidget);

    // Verify that the main features are displayed
    expect(find.text('أحداث السيرة النبوية'), findsOneWidget);
    expect(find.text('الأحاديث النبوية'), findsOneWidget);
    expect(find.text('البحث والفهرسة'), findsOneWidget);
  });

  testWidgets('Navigation test', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());

    // Find and tap the Seerah events card
    final seerahCard = find.text('أحداث السيرة النبوية');
    expect(seerahCard, findsOneWidget);

    await tester.tap(seerahCard);
    await tester.pumpAndSettle();

    // Verify navigation to Seerah events screen
    expect(find.text('أحداث السيرة النبوية الشريفة'), findsOneWidget);
  });

  testWidgets('Hadith navigation test', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());

    // Find and tap the Hadith card
    final hadithCard = find.text('الأحاديث النبوية');
    expect(hadithCard, findsOneWidget);

    await tester.tap(hadithCard);
    await tester.pumpAndSettle();

    // Verify navigation to Hadith screen
    expect(find.text('الأحاديث النبوية الشريفة'), findsOneWidget);
  });

  testWidgets('Search navigation test', (WidgetTester tester) async {
    await tester.pumpWidget(const MyApp());

    // Find and tap the Search card
    final searchCard = find.text('البحث والفهرسة');
    expect(searchCard, findsOneWidget);

    await tester.tap(searchCard);
    await tester.pumpAndSettle();

    // Verify navigation to Search screen
    expect(find.text('البحث المتقدم'), findsOneWidget);
  });
}
