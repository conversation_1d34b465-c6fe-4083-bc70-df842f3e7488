                        -HD:\def\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=28
-DANDROID_PLATFORM=android-28
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\AndroidstudioSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\AndroidstudioSDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\AndroidstudioSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\AndroidstudioSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\sserah app n15\ttt1\seerah_app\build\app\intermediates\cxx\Debug\2h1s1d1r\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\sserah app n15\ttt1\seerah_app\build\app\intermediates\cxx\Debug\2h1s1d1r\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BD:\sserah app n15\ttt1\seerah_app\android\app\.cxx\Debug\2h1s1d1r\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2