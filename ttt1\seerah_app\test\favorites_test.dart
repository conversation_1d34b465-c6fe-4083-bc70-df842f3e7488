import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../lib/providers/favorites_provider.dart';

void main() {
  group('FavoritesProvider Tests', () {
    late FavoritesProvider favoritesProvider;

    setUp(() async {
      // إعداد SharedPreferences للاختبار
      SharedPreferences.setMockInitialValues({});
      favoritesProvider = FavoritesProvider();
      await favoritesProvider.initializeFavorites();
    });

    test('should initialize with empty favorites', () {
      expect(favoritesProvider.favorites.isEmpty, true);
      expect(favoritesProvider.isLoading, false);
      expect(favoritesProvider.error, null);
    });

    test('should add item to favorites', () async {
      // إضافة عنصر للمفضلة
      await favoritesProvider.addToFavorites('test_id', FavoriteType.seerah);
      
      expect(favoritesProvider.favorites.length, 1);
      expect(favoritesProvider.isFavorite('test_id', FavoriteType.seerah), true);
    });

    test('should remove item from favorites', () async {
      // إضافة عنصر أولاً
      await favoritesProvider.addToFavorites('test_id', FavoriteType.seerah);
      expect(favoritesProvider.favorites.length, 1);
      
      // إزالة العنصر
      await favoritesProvider.removeFromFavorites('test_id', FavoriteType.seerah);
      expect(favoritesProvider.favorites.length, 0);
      expect(favoritesProvider.isFavorite('test_id', FavoriteType.seerah), false);
    });

    test('should toggle favorite correctly', () async {
      // التبديل من غير مفضل إلى مفضل
      await favoritesProvider.toggleFavorite('test_id', FavoriteType.hadith);
      expect(favoritesProvider.isFavorite('test_id', FavoriteType.hadith), true);
      
      // التبديل من مفضل إلى غير مفضل
      await favoritesProvider.toggleFavorite('test_id', FavoriteType.hadith);
      expect(favoritesProvider.isFavorite('test_id', FavoriteType.hadith), false);
    });

    test('should get favorites by type', () async {
      // إضافة عناصر من أنواع مختلفة
      await favoritesProvider.addToFavorites('seerah_1', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('hadith_1', FavoriteType.hadith);
      await favoritesProvider.addToFavorites('companion_1', FavoriteType.companion);
      
      expect(favoritesProvider.getFavoritesByType(FavoriteType.seerah).length, 1);
      expect(favoritesProvider.getFavoritesByType(FavoriteType.hadith).length, 1);
      expect(favoritesProvider.getFavoritesByType(FavoriteType.companion).length, 1);
    });

    test('should get favorites count by type', () async {
      // إضافة عناصر متعددة من نفس النوع
      await favoritesProvider.addToFavorites('seerah_1', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('seerah_2', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('hadith_1', FavoriteType.hadith);
      
      expect(favoritesProvider.getFavoritesCountByType(FavoriteType.seerah), 2);
      expect(favoritesProvider.getFavoritesCountByType(FavoriteType.hadith), 1);
      expect(favoritesProvider.getFavoritesCountByType(FavoriteType.companion), 0);
    });

    test('should clear all favorites', () async {
      // إضافة عدة عناصر
      await favoritesProvider.addToFavorites('seerah_1', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('hadith_1', FavoriteType.hadith);
      await favoritesProvider.addToFavorites('companion_1', FavoriteType.companion);
      
      expect(favoritesProvider.favorites.length, 3);
      
      // مسح الكل
      await favoritesProvider.clearAllFavorites();
      expect(favoritesProvider.favorites.length, 0);
    });

    test('should clear favorites by type', () async {
      // إضافة عناصر من أنواع مختلفة
      await favoritesProvider.addToFavorites('seerah_1', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('seerah_2', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('hadith_1', FavoriteType.hadith);
      
      expect(favoritesProvider.favorites.length, 3);
      
      // مسح السيرة فقط
      await favoritesProvider.clearFavoritesByType(FavoriteType.seerah);
      expect(favoritesProvider.favorites.length, 1);
      expect(favoritesProvider.getFavoritesCountByType(FavoriteType.seerah), 0);
      expect(favoritesProvider.getFavoritesCountByType(FavoriteType.hadith), 1);
    });

    test('should not add duplicate favorites', () async {
      // إضافة نفس العنصر مرتين
      await favoritesProvider.addToFavorites('test_id', FavoriteType.seerah);
      await favoritesProvider.addToFavorites('test_id', FavoriteType.seerah);
      
      // يجب أن يبقى عنصر واحد فقط
      expect(favoritesProvider.favorites.length, 1);
    });

    test('should persist favorites across sessions', () async {
      // إضافة عنصر
      await favoritesProvider.addToFavorites('persistent_id', FavoriteType.hadith);
      expect(favoritesProvider.favorites.length, 1);
      
      // إنشاء provider جديد لمحاكاة جلسة جديدة
      final newProvider = FavoritesProvider();
      await newProvider.initializeFavorites();
      
      // يجب أن يكون العنصر محفوظ
      expect(newProvider.favorites.length, 1);
      expect(newProvider.isFavorite('persistent_id', FavoriteType.hadith), true);
    });
  });
}

// اختبار تكامل للتأكد من عمل النظام مع البيانات الحقيقية
void integrationTests() {
  group('Favorites Integration Tests', () {
    test('should work with real data', () async {
      final provider = FavoritesProvider();
      await provider.initializeFavorites();
      
      // محاولة إضافة بيانات حقيقية
      try {
        await provider.addToFavorites('seerah_1', FavoriteType.seerah);
        await provider.addToFavorites('hadith_1', FavoriteType.hadith);
        await provider.addToFavorites('companion_1', FavoriteType.companion);
        
        expect(provider.favorites.length, 3);
        expect(provider.error, null);
        
        print('✅ اختبار التكامل نجح - تم إضافة ${provider.favorites.length} عناصر للمفضلة');
      } catch (e) {
        print('❌ خطأ في اختبار التكامل: $e');
        fail('Integration test failed: $e');
      }
    });
  });
}
