import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/companion.dart';
import '../providers/favorites_provider.dart';

class CompanionCard extends StatelessWidget {
  final Companion companion;
  final VoidCallback onTap;

  const CompanionCard({
    super.key,
    required this.companion,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              _buildBiography(),
              const SizedBox(height: 12),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildAvatar(),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                companion.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2E7D32),
                  fontFamily: 'Amiri',
                ),
              ),
              if (companion.nickname.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    companion.nickname,
                    style: const TextStyle(
                      fontSize: 13,
                      color: Color(0xFF2E7D32),
                      fontFamily: 'Amiri',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 12),
              _buildCategoryChip(),
            ],
          ),
        ),
        _buildBookmarkIcon(),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: _getCategoryColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: _getCategoryColor().withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Center(
        child: Text(
          _getInitials(),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: _getCategoryColor(),
            fontFamily: 'Amiri',
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: _getCategoryColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getCategoryColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        companion.category,
        style: TextStyle(
          fontSize: 10,
          color: _getCategoryColor(),
          fontFamily: 'Amiri',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildBiography() {
    return Text(
      companion.biography,
      style: const TextStyle(
        fontSize: 14,
        color: Color(0xFF444444),
        fontFamily: 'Amiri',
        height: 1.5,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        _buildInfoChip(
          icon: Icons.location_on,
          text: companion.birthPlace,
          color: Colors.blue,
        ),
        const SizedBox(width: 8),
        _buildInfoChip(
          icon: Icons.calendar_today,
          text: '${companion.birthYear} - ${companion.deathYear}',
          color: Colors.orange,
        ),
        const Spacer(),
        Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey[400],
        ),
      ],
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookmarkIcon() {
    return Consumer<FavoritesProvider>(
      builder: (context, favoritesProvider, child) {
        final isFavorite = favoritesProvider.isFavorite(
          companion.id,
          FavoriteType.companion,
        );
        return GestureDetector(
          onTap: () {
            favoritesProvider.toggleFavorite(
              companion.id,
              FavoriteType.companion,
            );
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  isFavorite
                      ? 'تم إزالة الصحابي من المفضلة'
                      : 'تم إضافة الصحابي للمفضلة',
                  style: const TextStyle(fontFamily: 'Amiri'),
                ),
                backgroundColor: isFavorite ? Colors.orange : Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: isFavorite
                  ? Colors.red.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              size: 16,
              color: isFavorite ? Colors.red : Colors.grey,
            ),
          ),
        );
      },
    );
  }

  String _getInitials() {
    final words = companion.name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}';
    } else if (words.isNotEmpty) {
      return words[0][0];
    }
    return 'ص';
  }

  Color _getCategoryColor() {
    switch (companion.category) {
      case 'العشرة المبشرون بالجنة':
        return const Color(0xFF4CAF50);
      case 'أمهات المؤمنين':
        return const Color(0xFFE91E63);
      case 'الأنصار':
        return const Color(0xFF2196F3);
      case 'المهاجرون':
        return const Color(0xFFFF9800);
      case 'الصحابيات':
        return const Color(0xFF9C27B0);
      default:
        return const Color(0xFF607D8B);
    }
  }
}
