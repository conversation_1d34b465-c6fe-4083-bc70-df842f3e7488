import 'package:flutter/material.dart';
import '../data/seerah_data.dart';
import '../data/hadith_data.dart';
import '../data/companions_data.dart';

enum SearchCategory { all, seerah, hadith, companions }
enum SortBy { relevance, date, alphabetical }

class SearchResult {
  final String id;
  final String title;
  final String content;
  final String category;
  final double relevanceScore;
  final dynamic originalData;

  SearchResult({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.relevanceScore,
    required this.originalData,
  });
}

class SearchProvider extends ChangeNotifier {
  String _query = '';
  SearchCategory _category = SearchCategory.all;
  SortBy _sortBy = SortBy.relevance;
  List<SearchResult> _results = [];
  bool _isSearching = false;
  final List<String> _searchHistory = [];
  final List<String> _favorites = [];

  // Getters
  String get query => _query;
  SearchCategory get category => _category;
  SortBy get sortBy => _sortBy;
  List<SearchResult> get results => _results;
  bool get isSearching => _isSearching;
  List<String> get searchHistory => _searchHistory;
  List<String> get favorites => _favorites;

  // Check if there are active filters
  bool get hasActiveFilters => _category != SearchCategory.all || _sortBy != SortBy.relevance;

  // Search methods
  Future<void> search(String query) async {
    if (query.trim().isEmpty) {
      _results.clear();
      notifyListeners();
      return;
    }

    _query = query.trim();
    _isSearching = true;
    notifyListeners();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate search delay

      _results = await _performSearch(_query);
      _addToHistory(_query);

    } catch (e) {
      debugPrint('خطأ في البحث: $e');
      _results.clear();
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  Future<List<SearchResult>> _performSearch(String query) async {
    List<SearchResult> allResults = [];

    // Search in Seerah events
    if (_category == SearchCategory.all || _category == SearchCategory.seerah) {
      final seerahResults = await _searchSeerah(query);
      allResults.addAll(seerahResults);
    }

    // Search in Hadiths
    if (_category == SearchCategory.all || _category == SearchCategory.hadith) {
      final hadithResults = await _searchHadiths(query);
      allResults.addAll(hadithResults);
    }

    // Search in Companions
    if (_category == SearchCategory.all || _category == SearchCategory.companions) {
      final companionResults = await _searchCompanions(query);
      allResults.addAll(companionResults);
    }

    // Sort results
    _sortResults(allResults);

    return allResults;
  }

  Future<List<SearchResult>> _searchSeerah(String query) async {
    final events = SeerahData.getAllEvents();
    List<SearchResult> results = [];

    for (final event in events) {
      double score = _calculateRelevanceScore(query, [
        event.title,
        event.description,
        event.location,
        event.category,
      ]);

      if (score > 0) {
        results.add(SearchResult(
          id: 'seerah_${event.id}',
          title: event.title,
          content: event.description,
          category: 'أحداث السيرة',
          relevanceScore: score,
          originalData: event,
        ));
      }
    }

    return results;
  }

  Future<List<SearchResult>> _searchHadiths(String query) async {
    final hadiths = HadithData.getAllHadiths();
    List<SearchResult> results = [];

    for (final hadith in hadiths) {
      double score = _calculateRelevanceScore(query, [
        hadith.arabicText,
        hadith.translation,
        hadith.explanation,
        hadith.category,
        hadith.narrator,
      ]);

      if (score > 0) {
        results.add(SearchResult(
          id: 'hadith_${hadith.id}',
          title: 'حديث: ${hadith.narrator}',
          content: hadith.arabicText,
          category: 'الأحاديث',
          relevanceScore: score,
          originalData: hadith,
        ));
      }
    }

    return results;
  }

  Future<List<SearchResult>> _searchCompanions(String query) async {
    final companions = CompanionsData.getAllCompanions();
    List<SearchResult> results = [];

    for (final companion in companions) {
      double score = _calculateRelevanceScore(query, [
        companion.name,
        companion.biography,
        companion.category,
      ]);

      if (score > 0) {
        results.add(SearchResult(
          id: 'companion_${companion.id}',
          title: companion.name,
          content: companion.biography,
          category: 'الصحابة',
          relevanceScore: score,
          originalData: companion,
        ));
      }
    }

    return results;
  }

  double _calculateRelevanceScore(String query, List<String> searchFields) {
    double score = 0.0;
    final queryWords = query.toLowerCase().split(' ');

    for (final field in searchFields) {
      final fieldLower = field.toLowerCase();

      for (final word in queryWords) {
        if (word.length < 2) continue;

        // Exact match gets highest score
        if (fieldLower.contains(word)) {
          score += 10.0;
        }

        // Partial match gets lower score
        for (int i = 0; i < fieldLower.length - word.length + 1; i++) {
          final substring = fieldLower.substring(i, i + word.length);
          if (_calculateSimilarity(word, substring) > 0.7) {
            score += 5.0;
          }
        }
      }
    }

    return score;
  }

  double _calculateSimilarity(String a, String b) {
    if (a == b) return 1.0;
    if (a.isEmpty || b.isEmpty) return 0.0;

    int matches = 0;
    int minLength = a.length < b.length ? a.length : b.length;

    for (int i = 0; i < minLength; i++) {
      if (a[i] == b[i]) matches++;
    }

    return matches / minLength;
  }

  void _sortResults(List<SearchResult> results) {
    switch (_sortBy) {
      case SortBy.relevance:
        results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
        break;
      case SortBy.alphabetical:
        results.sort((a, b) => a.title.compareTo(b.title));
        break;
      case SortBy.date:
        // For now, sort by relevance as we don't have date fields
        results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
        break;
    }
  }

  // Filter and sort methods
  void setCategory(SearchCategory category) {
    if (_category != category) {
      _category = category;
      if (_query.isNotEmpty) {
        search(_query);
      } else {
        notifyListeners();
      }
    }
  }

  void setSortBy(SortBy sortBy) {
    if (_sortBy != sortBy) {
      _sortBy = sortBy;
      _sortResults(_results);
      notifyListeners();
    }
  }

  // History methods
  void _addToHistory(String query) {
    if (!_searchHistory.contains(query)) {
      _searchHistory.insert(0, query);
      if (_searchHistory.length > 10) {
        _searchHistory.removeLast();
      }
    }
  }

  void clearHistory() {
    _searchHistory.clear();
    notifyListeners();
  }

  void removeFromHistory(String query) {
    _searchHistory.remove(query);
    notifyListeners();
  }

  // Favorites methods
  void addToFavorites(String query) {
    if (!_favorites.contains(query)) {
      _favorites.add(query);
      notifyListeners();
    }
  }

  void removeFromFavorites(String query) {
    _favorites.remove(query);
    notifyListeners();
  }

  bool isFavorite(String query) {
    return _favorites.contains(query);
  }

  // Clear methods
  void clearResults() {
    _results.clear();
    _query = '';
    notifyListeners();
  }

  void clearAll() {
    _results.clear();
    _query = '';
    _searchHistory.clear();
    _favorites.clear();
    _category = SearchCategory.all;
    _sortBy = SortBy.relevance;
    notifyListeners();
  }
}
