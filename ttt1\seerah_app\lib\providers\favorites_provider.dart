import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/seerah_data.dart';
import '../data/hadith_data.dart';
import '../data/companions_data.dart';

enum FavoriteType {
  seerah,
  hadith,
  companion,
}

class FavoriteItem {
  final String id;
  final String title;
  final String subtitle;
  final FavoriteType type;
  final DateTime addedAt;

  FavoriteItem({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.type,
    required this.addedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'type': type.index,
      'addedAt': addedAt.millisecondsSinceEpoch,
    };
  }

  factory FavoriteItem.fromJson(Map<String, dynamic> json) {
    return FavoriteItem(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
      type: FavoriteType.values[json['type']],
      addedAt: DateTime.fromMillisecondsSinceEpoch(json['addedAt']),
    );
  }
}

class FavoritesProvider extends ChangeNotifier {
  List<FavoriteItem> _favorites = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<FavoriteItem> get favorites => _favorites;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get favorites by type
  List<FavoriteItem> getFavoritesByType(FavoriteType type) {
    return _favorites.where((item) => item.type == type).toList();
  }

  // Get favorites count by type
  int getFavoritesCountByType(FavoriteType type) {
    return _favorites.where((item) => item.type == type).length;
  }

  // Get all seerah favorites
  List<FavoriteItem> get seerahFavorites => getFavoritesByType(FavoriteType.seerah);

  // Get all hadith favorites
  List<FavoriteItem> get hadithFavorites => getFavoritesByType(FavoriteType.hadith);

  // Get all companion favorites
  List<FavoriteItem> get companionFavorites => getFavoritesByType(FavoriteType.companion);

  // Check if item is favorite
  bool isFavorite(String id, FavoriteType type) {
    return _favorites.any((item) => item.id == id && item.type == type);
  }

  // Initialize favorites
  Future<void> initializeFavorites() async {
    try {
      _setLoading(true);
      _setError(null);
      await _loadFavorites();
      _setLoading(false);
    } catch (e) {
      _setError('فشل في تحميل المفضلة: ${e.toString()}');
      _setLoading(false);
    }
  }

  // Add to favorites
  Future<void> addToFavorites(String id, FavoriteType type) async {
    try {
      if (isFavorite(id, type)) return;

      String title = '';
      String subtitle = '';

      try {
        switch (type) {
          case FavoriteType.seerah:
            final events = SeerahData.getAllEvents();
            final event = events.firstWhere((e) => e.id == id, orElse: () => throw Exception('Event not found'));
            title = event.title;
            subtitle = event.description;
            break;
          case FavoriteType.hadith:
            final hadiths = HadithData.getAllHadiths();
            final hadith = hadiths.firstWhere((h) => h.id == id, orElse: () => throw Exception('Hadith not found'));
            title = hadith.arabicText.length > 50
                ? '${hadith.arabicText.substring(0, 50)}...'
                : hadith.arabicText;
            subtitle = hadith.category;
            break;
          case FavoriteType.companion:
            final companions = CompanionsData.getAllCompanions();
            final companion = companions.firstWhere((c) => c.id == id, orElse: () => throw Exception('Companion not found'));
            title = companion.name;
            subtitle = companion.nickname;
            break;
        }
      } catch (e) {
        // إذا لم نجد البيانات، نستخدم قيم افتراضية للاختبار
        title = 'عنصر مفضل ($id)';
        subtitle = _getTypeNameArabic(type);
        debugPrint('تحذير: لم يتم العثور على البيانات للعنصر $id، استخدام قيم افتراضية');
      }

      final favoriteItem = FavoriteItem(
        id: id,
        title: title,
        subtitle: subtitle,
        type: type,
        addedAt: DateTime.now(),
      );

      _favorites.add(favoriteItem);
      await _saveFavorites();
      notifyListeners();
    } catch (e) {
      _setError('فشل في إضافة المفضلة: ${e.toString()}');
      debugPrint('خطأ في إضافة المفضلة: $e');
    }
  }

  // Helper method to get type name in Arabic
  String _getTypeNameArabic(FavoriteType type) {
    switch (type) {
      case FavoriteType.seerah:
        return 'السيرة النبوية';
      case FavoriteType.hadith:
        return 'الأحاديث النبوية';
      case FavoriteType.companion:
        return 'الصحابة الكرام';
    }
  }

  // Remove from favorites
  Future<void> removeFromFavorites(String id, FavoriteType type) async {
    try {
      _favorites.removeWhere((item) => item.id == id && item.type == type);
      await _saveFavorites();
      notifyListeners();
    } catch (e) {
      _setError('فشل في إزالة المفضلة: ${e.toString()}');
    }
  }

  // Toggle favorite
  Future<void> toggleFavorite(String id, FavoriteType type) async {
    if (isFavorite(id, type)) {
      await removeFromFavorites(id, type);
    } else {
      await addToFavorites(id, type);
    }
  }

  // Clear all favorites
  Future<void> clearAllFavorites() async {
    try {
      _favorites.clear();
      await _saveFavorites();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مسح المفضلة: ${e.toString()}');
    }
  }

  // Clear favorites by type
  Future<void> clearFavoritesByType(FavoriteType type) async {
    try {
      _favorites.removeWhere((item) => item.type == type);
      await _saveFavorites();
      notifyListeners();
    } catch (e) {
      _setError('فشل في مسح المفضلة: ${e.toString()}');
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load favorites from SharedPreferences
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getStringList('favorites') ?? [];

      _favorites = favoritesJson.map((jsonString) {
        try {
          final parts = jsonString.split('|');
          if (parts.length >= 5) {
            return FavoriteItem(
              id: parts[0],
              title: parts[1],
              subtitle: parts[2],
              type: FavoriteType.values[int.parse(parts[3])],
              addedAt: DateTime.fromMillisecondsSinceEpoch(int.parse(parts[4])),
            );
          }
        } catch (e) {
          debugPrint('خطأ في تحليل عنصر المفضلة: $e');
        }
        return null;
      }).where((item) => item != null).cast<FavoriteItem>().toList();

      // Sort by added date (newest first)
      _favorites.sort((a, b) => b.addedAt.compareTo(a.addedAt));
    } catch (e) {
      debugPrint('خطأ في تحميل المفضلة: $e');
      _favorites = [];
    }
  }

  // Save favorites to SharedPreferences
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = _favorites.map((item) {
        return '${item.id}|${item.title}|${item.subtitle}|${item.type.index}|${item.addedAt.millisecondsSinceEpoch}';
      }).toList();

      await prefs.setStringList('favorites', favoritesJson);
    } catch (e) {
      debugPrint('خطأ في حفظ المفضلة: $e');
    }
  }
}
