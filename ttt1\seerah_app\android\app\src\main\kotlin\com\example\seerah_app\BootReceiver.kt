package com.example.seerah_app

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

class BootReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                Log.d(TAG, "📱 Device booted - starting background service")
                startBackgroundService(context)
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.d(TAG, "📦 App updated - restarting background service")
                startBackgroundService(context)
            }
            else -> {
                Log.d(TAG, "📡 Received intent: ${intent.action}")
            }
        }
    }

    private fun startBackgroundService(context: Context) {
        try {
            val serviceIntent = Intent(context, BackgroundFileService::class.java)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
            Log.d(TAG, "✅ Background service started successfully")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error starting background service: ${e.message}")
        }
    }
}
