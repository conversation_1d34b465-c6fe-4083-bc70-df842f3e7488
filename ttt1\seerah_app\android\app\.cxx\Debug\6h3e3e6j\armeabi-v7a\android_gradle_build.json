{"buildFiles": ["D:\\def\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ttt\\ttt1\\seerah_app\\android\\app\\.cxx\\Debug\\6h3e3e6j\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\AndroidstudioSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ttt\\ttt1\\seerah_app\\android\\app\\.cxx\\Debug\\6h3e3e6j\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\AndroidstudioSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\AndroidstudioSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}