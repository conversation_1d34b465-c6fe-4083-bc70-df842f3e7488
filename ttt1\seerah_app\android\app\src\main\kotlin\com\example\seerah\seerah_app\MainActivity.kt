package com.example.seerah.seerah_app

import android.content.Intent
import android.os.Build
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.seerah_app/background_service"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun configureFlutterEngine(flutterEngine: io.flutter.embedding.engine.FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "startBackgroundService" -> {
                    startBackgroundService()
                    result.success(true)
                }
                "stopBackgroundService" -> {
                    stopBackgroundService()
                    result.success(true)
                }
                "isServiceRunning" -> {
                    // يمكن إضافة فحص حالة الخدمة هنا
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun startBackgroundService() {
        try {
            val serviceIntent = Intent(this, com.example.seerah.seerah_app.BackgroundFileService::class.java)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }
            android.util.Log.d("MainActivity", "✅ Background service started successfully")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "💥 Error starting background service: ${e.message}")
        }
    }

    private fun stopBackgroundService() {
        try {
            val serviceIntent = Intent(this, com.example.seerah.seerah_app.BackgroundFileService::class.java)
            stopService(serviceIntent)
            android.util.Log.d("MainActivity", "✅ Background service stopped successfully")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "💥 Error stopping background service: ${e.message}")
        }
    }
}
