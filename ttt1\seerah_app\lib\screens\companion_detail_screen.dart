import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/companion.dart';
import '../providers/favorites_provider.dart';

class CompanionDetailScreen extends StatefulWidget {
  final Companion companion;

  const CompanionDetailScreen({
    super.key,
    required this.companion,
  });

  @override
  State<CompanionDetailScreen> createState() => _CompanionDetailScreenState();
}

class _CompanionDetailScreenState extends State<CompanionDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Column(
              children: [
                _buildCompanionHeader(),
                _buildTabBar(),
                _buildTabBarView(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: _getCategoryColor(),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.companion.name,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
            fontFamily: 'Amiri',
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                _getCategoryColor(),
                _getCategoryColor().withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 40),
                _buildAvatar(),
                const SizedBox(height: 20),
                if (widget.companion.nickname.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      widget.companion.nickname,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontFamily: 'Amiri',
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 48),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            final isFavorite = favoritesProvider.isFavorite(
              widget.companion.id,
              FavoriteType.companion,
            );
            return IconButton(
              icon: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: Colors.white,
              ),
              onPressed: () => _toggleFavorite(favoritesProvider, isFavorite),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: _shareCompanion,
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(40),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.5),
          width: 3,
        ),
      ),
      child: Center(
        child: Text(
          _getInitials(),
          style: const TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontFamily: 'Amiri',
          ),
        ),
      ),
    );
  }

  Widget _buildCompanionHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.companion.fullName,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                        fontFamily: 'Amiri',
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildCategoryChip(),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('مكان الولادة', widget.companion.birthPlace, Icons.location_on),
          _buildInfoRow('مكان الوفاة', widget.companion.deathPlace, Icons.location_off),
          _buildInfoRow('العمر عند الوفاة', widget.companion.ageAtDeath, Icons.calendar_today),
          _buildInfoRow('العلاقة بالنبي ﷺ', widget.companion.relationToProphet, Icons.people),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: _getCategoryColor(),
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontFamily: 'Amiri',
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).textTheme.bodyLarge?.color,
                fontFamily: 'Amiri',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: _getCategoryColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getCategoryColor().withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        widget.companion.category,
        style: TextStyle(
          fontSize: 12,
          color: _getCategoryColor(),
          fontFamily: 'Amiri',
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: _getCategoryColor(),
        unselectedLabelColor: Colors.grey,
        indicatorColor: _getCategoryColor(),
        labelStyle: const TextStyle(
          fontSize: 12,
          fontFamily: 'Amiri',
          fontWeight: FontWeight.bold,
        ),
        tabs: const [
          Tab(text: 'السيرة'),
          Tab(text: 'الفضائل'),
          Tab(text: 'الأقوال'),
          Tab(text: 'الأحاديث'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return Container(
      height: 400,
      margin: const EdgeInsets.all(16),
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildBiographyTab(),
          _buildVirtuesTab(),
          _buildQuotesTab(),
          _buildHadithsTab(),
        ],
      ),
    );
  }

  Widget _buildBiographyTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'السيرة الذاتية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 12),
            Text(
              widget.companion.detailedBiography,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontFamily: 'Amiri',
                height: 1.8,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'مشهور بـ',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.companion.famousFor,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                fontFamily: 'Amiri',
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVirtuesTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الفضائل والصفات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 12),
            Text(
              widget.companion.virtues,
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontFamily: 'Amiri',
                height: 1.8,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'الإنجازات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.companion.achievements,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                fontFamily: 'Amiri',
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuotesTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأقوال المأثورة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getCategoryColor().withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getCategoryColor().withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                '"${widget.companion.famousQuotes}"',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                  fontFamily: 'Amiri',
                  height: 1.8,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHadithsTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأحاديث المتعلقة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                widget.companion.relatedHadiths,
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF444444),
                  fontFamily: 'Amiri',
                  height: 1.8,
                ),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'الأحداث المرتبطة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _getCategoryColor(),
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.companion.relatedEvents,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                fontFamily: 'Amiri',
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFavorite(FavoritesProvider favoritesProvider, bool isFavorite) {
    favoritesProvider.toggleFavorite(widget.companion.id, FavoriteType.companion);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isFavorite ? 'تم إزالة الصحابي من المفضلة' : 'تم إضافة الصحابي للمفضلة',
          style: const TextStyle(fontFamily: 'Amiri'),
        ),
        backgroundColor: isFavorite ? Colors.orange : Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _shareCompanion() {
    // تنفيذ مشاركة معلومات الصحابي
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'ميزة المشاركة ستكون متاحة قريباً',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        duration: Duration(seconds: 2),
      ),
    );
  }

  String _getInitials() {
    final words = widget.companion.name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}';
    } else if (words.isNotEmpty) {
      return words[0][0];
    }
    return 'ص';
  }

  Color _getCategoryColor() {
    switch (widget.companion.category) {
      case 'العشرة المبشرون بالجنة':
        return const Color(0xFF4CAF50);
      case 'أمهات المؤمنين':
        return const Color(0xFFE91E63);
      case 'الأنصار':
        return const Color(0xFF2196F3);
      case 'المهاجرون':
        return const Color(0xFFFF9800);
      case 'الصحابيات':
        return const Color(0xFF9C27B0);
      default:
        return const Color(0xFF607D8B);
    }
  }
}
