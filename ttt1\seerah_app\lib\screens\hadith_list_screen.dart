import 'package:flutter/material.dart';
import '../models/hadith.dart';
import '../data/hadith_data.dart';
import '../widgets/hadith_card.dart';
import '../widgets/hadith_category_chip.dart';
import '../screens/hadith_detail_screen.dart';

class HadithListScreen extends StatefulWidget {
  const HadithListScreen({super.key});

  @override
  State<HadithListScreen> createState() => _HadithListScreenState();
}

class _HadithListScreenState extends State<HadithListScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _searchController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _searchAnimation;

  List<Hadith> _allHadiths = [];
  List<Hadith> _filteredHadiths = [];
  String _selectedCategory = 'الكل';
  String _searchQuery = '';
  bool _isLoading = true;
  bool _isSearchVisible = false;
  final TextEditingController _searchTextController = TextEditingController();

  final List<String> _categories = [
    'الكل',
    'أحاديث العقيدة',
    'أحاديث العبادة',
    'أحاديث الأخلاق',
    'أحاديث المعاملات',
    'أحاديث الآداب الاجتماعية',
    'أحاديث الدعاء والذكر',
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadHadiths();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _searchController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _searchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _searchController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _loadHadiths() async {
    setState(() => _isLoading = true);

    // محاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    _allHadiths = HadithData.getAllHadiths();
    _filteredHadiths = List.from(_allHadiths);

    setState(() => _isLoading = false);
    _animationController.forward();
  }

  void _filterHadiths() {
    setState(() {
      _filteredHadiths = _allHadiths.where((hadith) {
        final matchesCategory = _selectedCategory == 'الكل' ||
                               hadith.category == _selectedCategory;
        final matchesSearch = _searchQuery.isEmpty ||
                             hadith.matchesSearch(_searchQuery);
        return matchesCategory && matchesSearch;
      }).toList();
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (!_isSearchVisible) {
        _searchTextController.clear();
        _searchQuery = '';
        _filterHadiths();
      }
    });

    if (_isSearchVisible) {
      _searchController.forward();
    } else {
      _searchController.reverse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    _searchTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.6),
            ],
          ),
        ),
      ),
      title: const Text(
        'الأحاديث النبوية الشريفة',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          fontFamily: 'Amiri',
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(
            _isSearchVisible ? Icons.search_off : Icons.search,
            color: Colors.white,
          ),
          onPressed: _toggleSearch,
        ),
        IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white),
          onPressed: _showFilterDialog,
        ),
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الأحاديث النبوية الشريفة...',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          if (_isSearchVisible) _buildSearchBar(),
          _buildStatsHeader(),
          _buildCategoryFilters(),
          Expanded(child: _buildHadithsList()),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return SlideTransition(
      position: _searchAnimation.drive(
        Tween<Offset>(
          begin: const Offset(0, -1),
          end: Offset.zero,
        ),
      ),
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchTextController,
          decoration: InputDecoration(
            hintText: 'ابحث في النص العربي أو الترجمة أو الشرح...',
            hintStyle: const TextStyle(fontFamily: 'Amiri'),
            border: InputBorder.none,
            icon: Icon(Icons.search, color: Theme.of(context).colorScheme.primary),
          ),
          style: const TextStyle(fontFamily: 'Amiri'),
          onChanged: (value) {
            setState(() => _searchQuery = value);
            _filterHadiths();
          },
        ),
      ),
    );
  }

  Widget _buildStatsHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF9800), Color(0xFFFFC107)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('إجمالي الأحاديث', '${_allHadiths.length}', Icons.book),
          _buildStatItem('الأحاديث المعروضة', '${_filteredHadiths.length}', Icons.visibility),
          _buildStatItem('الفئات', '${_categories.length - 1}', Icons.category),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Amiri',
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontFamily: 'Amiri',
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilters() {
    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: HadithCategoryChip(
              label: category,
              isSelected: _selectedCategory == category,
              onSelected: (selected) {
                if (selected) {
                  setState(() => _selectedCategory = category);
                  _filterHadiths();
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildHadithsList() {
    if (_filteredHadiths.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredHadiths.length,
      itemBuilder: (context, index) {
        final hadith = _filteredHadiths[index];
        return HadithCard(
          hadith: hadith,
          onTap: () => _navigateToHadithDetail(hadith),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد أحاديث تطابق البحث',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontFamily: 'Amiri',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو الفلترة',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
              fontFamily: 'Amiri',
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الأحاديث', style: TextStyle(fontFamily: 'Amiri')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _categories.map((category) {
            return RadioListTile<String>(
              title: Text(category, style: const TextStyle(fontFamily: 'Amiri')),
              value: category,
              groupValue: _selectedCategory,
              onChanged: (value) {
                setState(() => _selectedCategory = value!);
                _filterHadiths();
                Navigator.pop(context);
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _navigateToHadithDetail(Hadith hadith) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            HadithDetailScreen(hadith: hadith),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: animation.drive(
              Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                  .chain(CurveTween(curve: Curves.easeInOut)),
            ),
            child: child,
          );
        },
      ),
    );
  }
}
