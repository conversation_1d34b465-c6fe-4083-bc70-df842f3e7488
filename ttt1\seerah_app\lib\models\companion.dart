class Companion {
  final String id;
  final String name;
  final String fullName;
  final String nickname;
  final String biography;
  final String detailedBiography;
  final String category;
  final String birthPlace;
  final String deathPlace;
  final String birthYear;
  final String deathYear;
  final String ageAtDeath;
  final String relationToProphet;
  final String famousFor;
  final String virtues;
  final String achievements;
  final String famousQuotes;
  final String relatedHadiths;
  final String relatedEvents;
  final bool isBookmarked;

  Companion({
    required this.id,
    required this.name,
    required this.fullName,
    required this.nickname,
    required this.biography,
    required this.detailedBiography,
    required this.category,
    required this.birthPlace,
    required this.deathPlace,
    required this.birthYear,
    required this.deathYear,
    required this.ageAtDeath,
    required this.relationToProphet,
    required this.famousFor,
    required this.virtues,
    required this.achievements,
    required this.famousQuotes,
    required this.relatedHadiths,
    required this.relatedEvents,
    this.isBookmarked = false,
  });

  factory Companion.fromJson(Map<String, dynamic> json) {
    return Companion(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      fullName: json['fullName'] ?? '',
      nickname: json['nickname'] ?? '',
      biography: json['biography'] ?? '',
      detailedBiography: json['detailedBiography'] ?? '',
      category: json['category'] ?? '',
      birthPlace: json['birthPlace'] ?? '',
      deathPlace: json['deathPlace'] ?? '',
      birthYear: json['birthYear'] ?? '',
      deathYear: json['deathYear'] ?? '',
      ageAtDeath: json['ageAtDeath'] ?? '',
      relationToProphet: json['relationToProphet'] ?? '',
      famousFor: json['famousFor'] ?? '',
      virtues: json['virtues'] ?? '',
      achievements: json['achievements'] ?? '',
      famousQuotes: json['famousQuotes'] ?? '',
      relatedHadiths: json['relatedHadiths'] ?? '',
      relatedEvents: json['relatedEvents'] ?? '',
      isBookmarked: json['isBookmarked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'fullName': fullName,
      'nickname': nickname,
      'biography': biography,
      'detailedBiography': detailedBiography,
      'category': category,
      'birthPlace': birthPlace,
      'deathPlace': deathPlace,
      'birthYear': birthYear,
      'deathYear': deathYear,
      'ageAtDeath': ageAtDeath,
      'relationToProphet': relationToProphet,
      'famousFor': famousFor,
      'virtues': virtues,
      'achievements': achievements,
      'famousQuotes': famousQuotes,
      'relatedHadiths': relatedHadiths,
      'relatedEvents': relatedEvents,
      'isBookmarked': isBookmarked,
    };
  }

  Companion copyWith({
    String? id,
    String? name,
    String? fullName,
    String? nickname,
    String? biography,
    String? detailedBiography,
    String? category,
    String? birthPlace,
    String? deathPlace,
    String? birthYear,
    String? deathYear,
    String? ageAtDeath,
    String? relationToProphet,
    String? famousFor,
    String? virtues,
    String? achievements,
    String? famousQuotes,
    String? relatedHadiths,
    String? relatedEvents,
    bool? isBookmarked,
  }) {
    return Companion(
      id: id ?? this.id,
      name: name ?? this.name,
      fullName: fullName ?? this.fullName,
      nickname: nickname ?? this.nickname,
      biography: biography ?? this.biography,
      detailedBiography: detailedBiography ?? this.detailedBiography,
      category: category ?? this.category,
      birthPlace: birthPlace ?? this.birthPlace,
      deathPlace: deathPlace ?? this.deathPlace,
      birthYear: birthYear ?? this.birthYear,
      deathYear: deathYear ?? this.deathYear,
      ageAtDeath: ageAtDeath ?? this.ageAtDeath,
      relationToProphet: relationToProphet ?? this.relationToProphet,
      famousFor: famousFor ?? this.famousFor,
      virtues: virtues ?? this.virtues,
      achievements: achievements ?? this.achievements,
      famousQuotes: famousQuotes ?? this.famousQuotes,
      relatedHadiths: relatedHadiths ?? this.relatedHadiths,
      relatedEvents: relatedEvents ?? this.relatedEvents,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }
}
