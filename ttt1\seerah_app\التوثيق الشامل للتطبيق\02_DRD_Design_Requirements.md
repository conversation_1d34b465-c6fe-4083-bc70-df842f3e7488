# وثيقة متطلبات التصميم (DRD) - تطبيق السيرة النبوية الشريفة

## 🎨 نظرة عامة على التصميم

### فلسفة التصميم
تطبيق السيرة النبوية الشريفة مصمم بفلسفة **البساطة والوضوح** مع **الجمالية الإسلامية**، يهدف إلى توفير تجربة مستخدم مريحة وممتعة للتعلم الديني.

### المبادئ الأساسية
1. **البساطة**: واجهة نظيفة وسهلة الفهم
2. **الوضوح**: معلومات منظمة ومقروءة
3. **الجمالية**: تصميم جذاب يحفز على التعلم
4. **الوظيفية**: كل عنصر له غرض واضح
5. **الاتساق**: نفس النمط في جميع الشاشات

---

## 🎯 تجربة المستخدم (UX)

### رحلة المستخدم الأساسية

#### 1. بدء التطبيق
```
شاشة التحميل (3 ثوان) → الصفحة الرئيسية
```
- **شاشة التحميل**: عرض اسم التطبيق مع مؤشر تحميل دائري
- **الانتقال**: تلقائي وسلس إلى الصفحة الرئيسية

#### 2. التنقل الرئيسي
```
الصفحة الرئيسية → اختيار القسم → عرض المحتوى → التفاصيل
```
- **الصفحة الرئيسية**: 3 بطاقات رئيسية + شريط علوي للبحث والإعدادات
- **اختيار القسم**: نقرة واحدة للوصول للمحتوى
- **عرض المحتوى**: قائمة منظمة مع بحث وفلترة
- **التفاصيل**: شاشة مخصصة لكل عنصر

#### 3. البحث والاستكشاف
```
أيقونة البحث → شاشة البحث المتقدم → النتائج → التفاصيل
```
- **البحث السريع**: من الشريط العلوي
- **البحث المتقدم**: شاشة مخصصة مع فلاتر
- **النتائج الفورية**: أثناء الكتابة
- **ترتيب ذكي**: حسب الصلة والأهمية

### نقاط التفاعل الرئيسية

#### التفاعلات الأساسية
- **النقر**: للانتقال والاختيار
- **التمرير**: للتصفح العمودي
- **السحب**: للتحديث (Pull to Refresh)
- **الضغط المطول**: لعرض خيارات إضافية

#### ردود الفعل البصرية
- **تأثيرات الضغط**: تغيير لون خفيف عند اللمس
- **انتقالات سلسة**: بين الشاشات (300ms)
- **تحميل تدريجي**: للمحتوى الطويل
- **رسائل التأكيد**: للإجراءات المهمة

---

## 🖼️ واجهة المستخدم (UI)

### نظام الألوان

#### الألوان الأساسية
```dart
// الألوان المتاحة للاختيار
final Map<String, Color> availableColors = {
  'أخضر': Color(0xFF4CAF50),      // اللون الافتراضي
  'أزرق': Color(0xFF2196F3),
  'بنفسجي': Color(0xFF9C27B0),
  'برتقالي': Color(0xFFFF9800),
  'أزرق مخضر': Color(0xFF009688),
  'بني': Color(0xFF795548),
};
```

#### نظام الوضع الليلي/النهاري
```dart
// الوضع النهاري
ThemeData lightTheme = ThemeData(
  brightness: Brightness.light,
  primaryColor: selectedColor,
  scaffoldBackgroundColor: Color(0xFFF8F9FA),
  cardColor: Colors.white,
  textTheme: TextTheme(
    bodyLarge: TextStyle(color: Colors.black87),
    bodyMedium: TextStyle(color: Colors.black54),
  ),
);

// الوضع الليلي
ThemeData darkTheme = ThemeData(
  brightness: Brightness.dark,
  primaryColor: selectedColor,
  scaffoldBackgroundColor: Color(0xFF121212),
  cardColor: Color(0xFF1E1E1E),
  textTheme: TextTheme(
    bodyLarge: TextStyle(color: Colors.white),
    bodyMedium: TextStyle(color: Colors.white70),
  ),
);
```

### نظام الخطوط

#### الخط الأساسي
- **اسم الخط**: Amiri (خط عربي تقليدي)
- **الاستخدام**: جميع النصوص العربية
- **الأحجام المتاحة**: 12, 14, 16, 18, 20, 22, 24 بكسل

#### خط النظام (اختياري)
- **الاستخدام**: كبديل للمستخدمين الذين يفضلون خط النظام
- **التطبيق**: على جميع النصوص عند التفعيل

#### هرمية الخطوط
```dart
TextTheme textTheme = TextTheme(
  headlineLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
  headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
  titleLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
  titleMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
  bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
  bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
  bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
);
```

### مكونات الواجهة

#### 1. الشريط العلوي (AppBar)
```dart
AppBar(
  elevation: 0,
  backgroundColor: Colors.transparent,
  flexibleSpace: Container(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [primaryColor, primaryColor.withOpacity(0.8)],
      ),
    ),
  ),
  title: Text('عنوان الصفحة', style: TextStyle(fontFamily: 'Amiri')),
  actions: [IconButton(icon: Icon(Icons.search))],
)
```

#### 2. البطاقات (Cards)
```dart
Card(
  elevation: 4,
  borderRadius: BorderRadius.circular(16),
  child: Container(
    padding: EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Theme.of(context).cardColor,
      borderRadius: BorderRadius.circular(16),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.2),
          blurRadius: 8,
          offset: Offset(0, 2),
        ),
      ],
    ),
  ),
)
```

#### 3. أزرار التفاعل
```dart
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: Theme.of(context).primaryColor,
    foregroundColor: Colors.white,
    elevation: 2,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  ),
  child: Text('نص الزر', style: TextStyle(fontFamily: 'Amiri')),
)
```

---

## 📱 تخطيط الشاشات

### 1. الصفحة الرئيسية
```
┌─────────────────────────────────────┐
│ [🔍] السيرة النبوية الشريفة [⚙️] │ ← شريط علوي
├─────────────────────────────────────┤
│                                     │
│  🕌 السيرة النبوية                 │ ← بطاقة 1
│  تعلم أحداث حياة النبي ﷺ          │
│                                     │
│  📖 الأحاديث النبوية               │ ← بطاقة 2
│  أحاديث صحيحة مع الشرح             │
│                                     │
│  👥 الصحابة الكرام                 │ ← بطاقة 3
│  سير الصحابة والصحابيات            │
│                                     │
├─────────────────────────────────────┤
│ تطوير: وائل شايبي 2025 ❤️          │ ← تذييل
└─────────────────────────────────────┘
```

### 2. شاشة قائمة المحتوى
```
┌─────────────────────────────────────┐
│ [←] عنوان القسم              [🔍] │ ← شريط علوي
├─────────────────────────────────────┤
│ [🔍 البحث في المحتوى...]          │ ← شريط بحث
├─────────────────────────────────────┤
│                                     │
│ 📄 عنوان العنصر الأول              │ ← عنصر 1
│    وصف مختصر للمحتوى...           │
│                                     │
│ 📄 عنوان العنصر الثاني             │ ← عنصر 2
│    وصف مختصر للمحتوى...           │
│                                     │
│ 📄 عنوان العنصر الثالث             │ ← عنصر 3
│    وصف مختصر للمحتوى...           │
│                                     │
└─────────────────────────────────────┘
```

### 3. شاشة التفاصيل
```
┌─────────────────────────────────────┐
│ [←] عنوان المحتوى                 │ ← شريط علوي
├─────────────────────────────────────┤
│                                     │
│ 📊 معلومات أساسية                  │ ← قسم المعلومات
│ • التاريخ: ...                     │
│ • المكان: ...                      │
│ • الأهمية: ...                     │
│                                     │
│ 📝 المحتوى التفصيلي                │ ← قسم المحتوى
│ نص طويل يحتوي على التفاصيل...      │
│                                     │
│ 💡 الدروس المستفادة                │ ← قسم الدروس
│ • درس أول...                       │
│ • درس ثاني...                      │
│                                     │
└─────────────────────────────────────┘
```

---

## 🔧 مكونات التفاعل المتقدمة

### 1. نظام البحث المتقدم
```dart
// واجهة البحث
SearchDelegate(
  searchFieldLabel: 'ابحث في المحتوى...',
  searchFieldStyle: TextStyle(fontFamily: 'Amiri'),
  delegate: CustomSearchDelegate(),
)

// فلاتر البحث
Row(
  children: [
    FilterChip(label: Text('الكل'), selected: true),
    FilterChip(label: Text('السيرة'), selected: false),
    FilterChip(label: Text('الأحاديث'), selected: false),
    FilterChip(label: Text('الصحابة'), selected: false),
  ],
)
```

### 2. نظام الإعدادات
```dart
// شاشة الإعدادات
ListView(
  children: [
    // قسم المظهر
    ListTile(
      title: Text('المظهر', style: TextStyle(fontFamily: 'Amiri')),
      subtitle: Text('الوضع الليلي والألوان'),
    ),

    // قسم الخط
    ListTile(
      title: Text('الخط', style: TextStyle(fontFamily: 'Amiri')),
      subtitle: Text('حجم ونوع الخط'),
    ),

    // قسم حول التطبيق
    ListTile(
      title: Text('حول التطبيق', style: TextStyle(fontFamily: 'Amiri')),
      subtitle: Text('معلومات التطبيق والمطور'),
    ),
  ],
)
```

### 3. نموذج التواصل
```dart
// نموذج التواصل مع المطور
AlertDialog(
  title: Text('التواصل مع المطور'),
  content: Column(
    children: [
      TextField(
        decoration: InputDecoration(
          labelText: 'موضوع الرسالة',
          prefixIcon: Icon(Icons.subject),
        ),
      ),
      TextField(
        maxLines: 5,
        decoration: InputDecoration(
          labelText: 'نص الرسالة',
          prefixIcon: Icon(Icons.message),
        ),
      ),
    ],
  ),
  actions: [
    TextButton(child: Text('إلغاء')),
    ElevatedButton(child: Text('إرسال')),
  ],
)
```

---

## 🎭 الحالات والاستجابات

### حالات التحميل
```dart
// حالة التحميل
Center(
  child: Column(
    children: [
      CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation(primaryColor),
      ),
      SizedBox(height: 16),
      Text('جاري التحميل...', style: TextStyle(fontFamily: 'Amiri')),
    ],
  ),
)
```

### حالات الخطأ
```dart
// حالة عدم وجود نتائج
Center(
  child: Column(
    children: [
      Icon(Icons.search_off, size: 64, color: Colors.grey),
      Text('لا توجد نتائج', style: TextStyle(fontFamily: 'Amiri')),
      Text('جرب كلمات بحث أخرى', style: TextStyle(color: Colors.grey)),
    ],
  ),
)
```

### رسائل التأكيد
```dart
// رسالة نجاح
SnackBar(
  content: Text('تم الإرسال بنجاح!', style: TextStyle(fontFamily: 'Amiri')),
  backgroundColor: Colors.green,
  duration: Duration(seconds: 3),
)
```

---

## 📐 المقاييس والمسافات

### نظام الشبكة
- **الوحدة الأساسية**: 8px
- **المسافات الصغيرة**: 8px, 16px
- **المسافات المتوسطة**: 24px, 32px
- **المسافات الكبيرة**: 48px, 64px

### أحجام العناصر
```dart
// أحجام البطاقات
const double cardHeight = 120.0;
const double cardPadding = 16.0;
const double cardRadius = 16.0;

// أحجام الأيقونات
const double smallIcon = 16.0;
const double mediumIcon = 24.0;
const double largeIcon = 32.0;

// أحجام الأزرار
const double buttonHeight = 48.0;
const double buttonRadius = 8.0;
```

### نقاط الكسر (Breakpoints)
```dart
// أحجام الشاشات
const double mobileBreakpoint = 600.0;
const double tabletBreakpoint = 900.0;
const double desktopBreakpoint = 1200.0;
```

---

## 🎬 الحركات والانتقالات

### انتقالات الشاشات
```dart
// انتقال سلس بين الشاشات
PageRouteBuilder(
  transitionDuration: Duration(milliseconds: 300),
  pageBuilder: (context, animation, secondaryAnimation) => NextScreen(),
  transitionsBuilder: (context, animation, secondaryAnimation, child) {
    return SlideTransition(
      position: animation.drive(
        Tween(begin: Offset(1.0, 0.0), end: Offset.zero),
      ),
      child: child,
    );
  },
)
```

### تأثيرات التفاعل
```dart
// تأثير الضغط على البطاقات
InkWell(
  onTap: () => Navigator.push(...),
  borderRadius: BorderRadius.circular(16),
  child: Card(...),
)

// تأثير التحميل التدريجي
AnimatedBuilder(
  animation: _animationController,
  builder: (context, child) {
    return FadeTransition(
      opacity: _animation,
      child: SlideTransition(
        position: _slideAnimation,
        child: child,
      ),
    );
  },
)
```

---

## 📱 التصميم المتجاوب

### تكيف الشاشات
```dart
// تخطيط متجاوب للصفحة الرئيسية
LayoutBuilder(
  builder: (context, constraints) {
    if (constraints.maxWidth > 600) {
      // تخطيط الأجهزة اللوحية
      return GridView.count(crossAxisCount: 2, children: cards);
    } else {
      // تخطيط الهواتف
      return ListView(children: cards);
    }
  },
)
```

### تكيف النصوص
```dart
// حجم خط متجاوب
double getResponsiveFontSize(BuildContext context, double baseSize) {
  double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 600) {
    return baseSize * 1.2; // أكبر للأجهزة اللوحية
  }
  return baseSize; // حجم عادي للهواتف
}
```

---

## 🔧 التحسينات التقنية المطبقة

### إدارة الأيقونات
```dart
// نظام الأيقونات المحسن
flutter_launcher_icons:
  android: true
  ios: false
  image_path: "icons/icon.png"
  min_sdk_android: 21
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "icons/icon.png"

// استخدام الأيقونة في التطبيق
Image.asset(
  'assets/icon.png',
  width: 120,
  height: 120,
  fit: BoxFit.cover,
  errorBuilder: (context, error, stackTrace) {
    return Container(/* fallback icon */);
  },
)
```

### دعم أندرويد 12+ للإيميل
```xml
<!-- AndroidManifest.xml -->
<queries>
    <!-- Required for email functionality on Android 12+ -->
    <intent>
        <action android:name="android.intent.action.SENDTO"/>
        <data android:scheme="mailto"/>
    </intent>

    <!-- Alternative email intent for broader compatibility -->
    <intent>
        <action android:name="android.intent.action.SEND"/>
        <data android:mimeType="text/plain"/>
    </intent>

    <!-- Gmail specific intent -->
    <intent>
        <action android:name="android.intent.action.VIEW"/>
        <data android:scheme="mailto"/>
    </intent>
</queries>
```

### تحسين إرسال الإيميل
```dart
// محاولات متعددة للإرسال
bool emailLaunched = false;

try {
  // المحاولة الأولى: mailto عادي
  if (await canLaunchUrl(mailtoUri)) {
    await launchUrl(mailtoUri, mode: LaunchMode.externalApplication);
    emailLaunched = true;
  }
} catch (e) {
  // المحاولة الثانية: intent مباشر للأندرويد
  final Uri androidEmailUri = Uri.parse('intent://send?...');
  if (await canLaunchUrl(androidEmailUri)) {
    await launchUrl(androidEmailUri);
    emailLaunched = true;
  }
}
```

### تحسين الأداء
```dart
// Debounced saving للإعدادات
Timer? _saveTimer;

void _debouncedSave() {
  _saveTimer?.cancel();
  _saveTimer = Timer(const Duration(milliseconds: 500), () {
    _saveSettings();
  });
}

@override
void dispose() {
  _saveTimer?.cancel();
  super.dispose();
}
```

---

## ✅ معايير التصميم

### إمكانية الوصول
- **تباين الألوان**: نسبة 4.5:1 على الأقل
- **حجم اللمس**: 44px على الأقل
- **دعم قارئ الشاشة**: جميع العناصر
- **تنقل بالكيبورد**: مدعوم بالكامل

### الأداء البصري
- **معدل الإطارات**: 60 FPS ثابت
- **وقت الاستجابة**: أقل من 100ms
- **تحميل الصور**: تدريجي ومحسن
- **ذاكرة الرسوميات**: محسنة

### التوافق التقني
- **أندرويد**: 5.0+ (API 21) حتى أحدث إصدار
- **Package Visibility**: دعم كامل لأندرويد 12+
- **Gradle**: متوافق مع أحدث إصدارات
- **Flutter**: محسن لأحدث SDK

---

## 🆕 المميزات الجديدة - التصميم

### نظام المفضلة المتقدم

#### تصميم صفحة المفضلة
```dart
// تبويبات ديناميكية مع عدادات
TabBar(
  tabs: [
    Tab(text: 'الكل (${favoritesProvider.favorites.length})'),
    Tab(text: 'السيرة (${favoritesProvider.getFavoritesCountByType(FavoriteType.seerah)})'),
    Tab(text: 'الأحاديث (${favoritesProvider.getFavoritesCountByType(FavoriteType.hadith)})'),
    Tab(text: 'الصحابة (${favoritesProvider.getFavoritesCountByType(FavoriteType.companion)})'),
  ],
)

// بطاقات المفضلة مع معلومات تفصيلية
Card(
  child: ListTile(
    leading: CircleAvatar(/* أيقونة النوع */),
    title: Text(favorite.title),
    subtitle: Column(
      children: [
        Text(favorite.subtitle),
        Row(
          children: [
            Container(/* تصنيف النوع */),
            Spacer(),
            Text(_formatDate(favorite.addedAt)), // تاريخ الإضافة
          ],
        ),
      ],
    ),
    trailing: IconButton(/* حذف من المفضلة */),
  ),
)
```

#### أيقونات المفضلة في البطاقات
```dart
// أيقونة تفاعلية في كل بطاقة
Consumer<FavoritesProvider>(
  builder: (context, favoritesProvider, child) {
    final isFavorite = favoritesProvider.isFavorite(item.id, type);
    return IconButton(
      onPressed: () {
        favoritesProvider.toggleFavorite(item.id, type);
        // رسالة تأكيد ملونة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isFavorite ? 'تم إزالة من المفضلة' : 'تم إضافة للمفضلة'),
            backgroundColor: isFavorite ? Colors.orange : Colors.green,
          ),
        );
      },
      icon: Icon(
        isFavorite ? Icons.favorite : Icons.favorite_border,
        color: isFavorite ? Colors.red : Colors.grey,
      ),
    );
  },
)
```

### الفلاتر المتقدمة المحسنة

#### واجهة الفلاتر الجديدة
```dart
// فلاتر بصرية محسنة
Row(
  children: [
    Expanded(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: SearchCategory.values.map((category) {
            return FilterChip(
              label: Text(_getCategoryName(category)),
              selected: searchProvider.category == category,
              selectedColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
              checkmarkColor: Theme.of(context).colorScheme.primary,
              onSelected: (selected) {
                if (selected) searchProvider.setCategory(category);
              },
            );
          }).toList(),
        ),
      ),
    ),
    // زر الترتيب
    PopupMenuButton<SortBy>(/* خيارات الترتيب */),
    // زر الفلاتر المتقدمة
    IconButton(
      icon: const Icon(Icons.tune),
      onPressed: () => _showAdvancedFiltersDialog(searchProvider),
    ),
  ],
)

// عرض الفلاتر النشطة
if (searchProvider.hasActiveFilters)
  _buildActiveFilters(searchProvider),
```

#### حوار الفلاتر المتقدمة
```dart
AlertDialog(
  title: const Text('الفلاتر المتقدمة'),
  content: SizedBox(
    width: double.maxFinite,
    child: Column(
      children: [
        // قسم الفئات
        const Text('الفئة:', style: TextStyle(fontWeight: FontWeight.bold)),
        Wrap(
          children: SearchCategory.values.map((category) {
            return FilterChip(/* فلتر الفئة */);
          }).toList(),
        ),

        // قسم الترتيب
        const Text('ترتيب النتائج:', style: TextStyle(fontWeight: FontWeight.bold)),
        Column(
          children: SortBy.values.map((sortBy) {
            return RadioListTile<SortBy>(/* خيار الترتيب */);
          }).toList(),
        ),
      ],
    ),
  ),
  actions: [
    TextButton(/* إعادة تعيين */),
    ElevatedButton(/* تطبيق */),
  ],
)
```

### تحسينات واجهة المستخدم

#### زر المفضلة في الصفحة الرئيسية
```dart
// في هيدر الصفحة الرئيسية
Positioned(
  top: 0,
  right: 0,
  child: Row(
    children: [
      IconButton(
        onPressed: _navigateToFavorites,
        icon: const Icon(Icons.favorite, color: Colors.white),
        tooltip: 'المفضلة',
      ),
      IconButton(
        onPressed: _navigateToSettings,
        icon: const Icon(Icons.settings, color: Colors.white),
        tooltip: 'الإعدادات',
      ),
    ],
  ),
)
```

#### رسائل التأكيد المحسنة
```dart
// رسائل ملونة حسب نوع الإجراء
ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(
    content: Text(message, style: const TextStyle(fontFamily: 'Amiri')),
    backgroundColor: isSuccess ? Colors.green : Colors.orange,
    duration: const Duration(seconds: 2),
    action: SnackBarAction(
      label: 'موافق',
      textColor: Colors.white,
      onPressed: () {},
    ),
  ),
);
```

### ألوان وثيمات محسنة

#### نظام الألوان للمفضلة
```dart
// ألوان مختلفة لكل نوع
Color getTypeColor(FavoriteType type) {
  switch (type) {
    case FavoriteType.seerah:
      return Colors.blue;
    case FavoriteType.hadith:
      return Colors.orange;
    case FavoriteType.companion:
      return Colors.green;
  }
}

// أيقونات مميزة لكل نوع
IconData getTypeIcon(FavoriteType type) {
  switch (type) {
    case FavoriteType.seerah:
      return Icons.auto_stories;
    case FavoriteType.hadith:
      return Icons.menu_book;
    case FavoriteType.companion:
      return Icons.people;
  }
}
```

### التوافق
- **أحجام الشاشات**: من 4 إنش إلى 12 إنش
- **كثافة البكسل**: جميع الكثافات
- **اتجاه الشاشة**: عمودي (مفضل) وأفقي
- **أنظمة التشغيل**: Android 5.0+ و iOS 12.0+

---

*هذه الوثيقة تحدد جميع متطلبات التصميم والواجهة لتطبيق السيرة النبوية الشريفة بدقة تامة لضمان إعادة البناء بنسبة 100%*
