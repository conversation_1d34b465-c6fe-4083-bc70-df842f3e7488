// اختبار مباشر لوظيفة البحث المتقدم
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/providers/search_provider.dart';

void main() {
  runApp(SearchTestApp());
}

class SearchTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => SearchProvider(),
      child: MaterialApp(
        title: 'اختبار البحث',
        home: SearchTestScreen(),
      ),
    );
  }
}

class SearchTestScreen extends StatefulWidget {
  @override
  _SearchTestScreenState createState() => _SearchTestScreenState();
}

class _SearchTestScreenState extends State<SearchTestScreen> {
  final TextEditingController _controller = TextEditingController();
  List<String> _testQueries = [
    'محمد',
    'الهجرة',
    'بدر',
    'أبو بكر',
    'عائشة',
    'الصلاة',
    'القرآن',
    'مكة',
    'المدينة',
    'فتح',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('اختبار البحث المتقدم'),
        backgroundColor: Colors.blue,
      ),
      body: Consumer<SearchProvider>(
        builder: (context, searchProvider, child) {
          return Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                // معلومات الحالة
                _buildStatusCard(searchProvider),
                SizedBox(height: 16),
                
                // حقل البحث
                _buildSearchField(searchProvider),
                SizedBox(height: 16),
                
                // أزرار الاختبار السريع
                _buildQuickTestButtons(searchProvider),
                SizedBox(height: 16),
                
                // النتائج
                Expanded(
                  child: _buildResults(searchProvider),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(SearchProvider provider) {
    return Card(
      color: provider.isSearching ? Colors.orange[50] : Colors.green[50],
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  provider.isSearching ? Icons.search : Icons.check_circle,
                  color: provider.isSearching ? Colors.orange : Colors.green,
                ),
                SizedBox(width: 8),
                Text(
                  provider.isSearching ? 'جاري البحث...' : 'جاهز للبحث',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: provider.isSearching ? Colors.orange : Colors.green,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text('الكلمة المبحوثة: "${provider.query}"'),
            Text('عدد النتائج: ${provider.results.length}'),
            Text('الفئة: ${_getCategoryName(provider.category)}'),
            Text('الترتيب: ${_getSortName(provider.sortBy)}'),
            if (provider.searchHistory.isNotEmpty)
              Text('آخر بحث: ${provider.searchHistory.first}'),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchField(SearchProvider provider) {
    return TextField(
      controller: _controller,
      decoration: InputDecoration(
        labelText: 'ابحث هنا',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.search),
        suffixIcon: provider.isSearching
            ? SizedBox(
                width: 20,
                height: 20,
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : _controller.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      _controller.clear();
                      provider.clearResults();
                    },
                    icon: Icon(Icons.clear),
                  )
                : null,
      ),
      onChanged: (value) {
        print('📝 تغيير النص: "$value"');
        if (value.trim().isNotEmpty) {
          provider.search(value);
        } else {
          provider.clearResults();
        }
      },
      onSubmitted: (value) {
        print('📝 إرسال النص: "$value"');
        if (value.trim().isNotEmpty) {
          provider.search(value);
        }
      },
    );
  }

  Widget _buildQuickTestButtons(SearchProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختبارات سريعة:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: _testQueries.map((query) {
            return ElevatedButton(
              onPressed: () {
                _controller.text = query;
                provider.search(query);
              },
              child: Text(query),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[100],
                foregroundColor: Colors.blue[800],
              ),
            );
          }).toList(),
        ),
        SizedBox(height: 8),
        Row(
          children: [
            ElevatedButton(
              onPressed: () => provider.clearResults(),
              child: Text('مسح النتائج'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            ),
            SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => provider.clearHistory(),
              child: Text('مسح التاريخ'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildResults(SearchProvider provider) {
    if (provider.query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'ابدأ البحث',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    if (provider.isSearching) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري البحث...'),
          ],
        ),
      );
    }

    if (provider.results.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد نتائج',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            Text(
              'للكلمة: "${provider.query}"',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: provider.results.length,
      itemBuilder: (context, index) {
        final result = provider.results[index];
        return Card(
          margin: EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(
              result.title,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.content,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        result.category,
                        style: TextStyle(fontSize: 12, color: Colors.blue[800]),
                      ),
                    ),
                    Spacer(),
                    Text(
                      'الصلة: ${result.relevanceScore.toStringAsFixed(1)}',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ],
            ),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم النقر على: ${result.title}'),
                ),
              );
            },
          ),
        );
      },
    );
  }

  String _getCategoryName(SearchCategory category) {
    switch (category) {
      case SearchCategory.all:
        return 'الكل';
      case SearchCategory.seerah:
        return 'السيرة';
      case SearchCategory.hadith:
        return 'الأحاديث';
      case SearchCategory.companions:
        return 'الصحابة';
    }
  }

  String _getSortName(SortBy sortBy) {
    switch (sortBy) {
      case SortBy.relevance:
        return 'الصلة';
      case SortBy.alphabetical:
        return 'أبجدي';
      case SortBy.date:
        return 'التاريخ';
    }
  }
}
