class Hadith {
  final String id;
  final String arabicText;
  final String translation;
  final String explanation;
  final String realLifeExample;
  final String narrator;
  final String source;
  final String category;
  final String theme;
  final int number;
  final List<String> keywords;
  final bool isAuthentic;
  final bool isBookmarked;

  Hadith({
    required this.id,
    required this.arabicText,
    required this.translation,
    required this.explanation,
    required this.realLifeExample,
    required this.narrator,
    required this.source,
    required this.category,
    required this.theme,
    required this.number,
    required this.keywords,
    this.isAuthentic = true,
    this.isBookmarked = false,
  });

  factory Hadith.fromJson(Map<String, dynamic> json) {
    return Hadith(
      id: json['id'] ?? '',
      arabicText: json['arabicText'] ?? '',
      translation: json['translation'] ?? '',
      explanation: json['explanation'] ?? '',
      realLifeExample: json['realLifeExample'] ?? '',
      narrator: json['narrator'] ?? '',
      source: json['source'] ?? '',
      category: json['category'] ?? '',
      theme: json['theme'] ?? '',
      number: json['number'] ?? 0,
      keywords: List<String>.from(json['keywords'] ?? []),
      isAuthentic: json['isAuthentic'] ?? true,
      isBookmarked: json['isBookmarked'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'arabicText': arabicText,
      'translation': translation,
      'explanation': explanation,
      'realLifeExample': realLifeExample,
      'narrator': narrator,
      'source': source,
      'category': category,
      'theme': theme,
      'number': number,
      'keywords': keywords,
      'isAuthentic': isAuthentic,
      'isBookmarked': isBookmarked,
    };
  }

  Hadith copyWith({
    String? id,
    String? arabicText,
    String? translation,
    String? explanation,
    String? realLifeExample,
    String? narrator,
    String? source,
    String? category,
    String? theme,
    int? number,
    List<String>? keywords,
    bool? isAuthentic,
    bool? isBookmarked,
  }) {
    return Hadith(
      id: id ?? this.id,
      arabicText: arabicText ?? this.arabicText,
      translation: translation ?? this.translation,
      explanation: explanation ?? this.explanation,
      realLifeExample: realLifeExample ?? this.realLifeExample,
      narrator: narrator ?? this.narrator,
      source: source ?? this.source,
      category: category ?? this.category,
      theme: theme ?? this.theme,
      number: number ?? this.number,
      keywords: keywords ?? this.keywords,
      isAuthentic: isAuthentic ?? this.isAuthentic,
      isBookmarked: isBookmarked ?? this.isBookmarked,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Hadith && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Hadith(id: $id, number: $number, theme: $theme, category: $category)';
  }

  // Helper methods
  bool containsKeyword(String keyword) {
    return keywords.any((k) => k.toLowerCase().contains(keyword.toLowerCase()));
  }

  bool matchesSearch(String query) {
    final lowerQuery = query.toLowerCase();
    return arabicText.toLowerCase().contains(lowerQuery) ||
           translation.toLowerCase().contains(lowerQuery) ||
           explanation.toLowerCase().contains(lowerQuery) ||
           theme.toLowerCase().contains(lowerQuery) ||
           narrator.toLowerCase().contains(lowerQuery) ||
           keywords.any((k) => k.toLowerCase().contains(lowerQuery));
  }

  // Get difficulty level based on explanation length
  String get difficultyLevel {
    if (explanation.length < 200) return 'سهل';
    if (explanation.length < 400) return 'متوسط';
    return 'متقدم';
  }

  // Get estimated reading time in minutes
  int get estimatedReadingTime {
    final totalWords = (arabicText.split(' ').length +
                       translation.split(' ').length +
                       explanation.split(' ').length +
                       realLifeExample.split(' ').length);
    return (totalWords / 200).ceil(); // Assuming 200 words per minute
  }

  // Get color based on category
  String get categoryColor {
    switch (category) {
      case 'أحاديث العقيدة':
        return '#4CAF50'; // Green
      case 'أحاديث العبادة':
        return '#2196F3'; // Blue
      case 'أحاديث الأخلاق':
        return '#FF9800'; // Orange
      case 'أحاديث المعاملات':
        return '#9C27B0'; // Purple
      case 'أحاديث الآداب الاجتماعية':
        return '#F44336'; // Red
      case 'أحاديث الدعاء والذكر':
        return '#607D8B'; // Blue Grey
      default:
        return '#795548'; // Brown
    }
  }

  // Get emoji based on theme
  String get themeEmoji {
    if (theme.contains('النية')) return '💭';
    if (theme.contains('الإحسان')) return '✨';
    if (theme.contains('أركان')) return '🕌';
    if (theme.contains('الرحمة')) return '❤️';
    if (theme.contains('الأخلاق')) return '🌟';
    if (theme.contains('الكلام')) return '💬';
    if (theme.contains('الصلاة')) return '🤲';
    if (theme.contains('الصوم')) return '🌙';
    if (theme.contains('الحج')) return '🕋';
    if (theme.contains('الصدقة')) return '💰';
    if (theme.contains('القرآن')) return '📖';
    if (theme.contains('الذكر')) return '📿';
    if (theme.contains('الدعاء')) return '🤲';
    if (theme.contains('الصبر')) return '💪';
    if (theme.contains('الحياء')) return '😌';
    if (theme.contains('الغضب')) return '😤';
    if (theme.contains('التجارة')) return '🏪';
    if (theme.contains('العمل')) return '⚒️';
    if (theme.contains('العدل')) return '⚖️';
    if (theme.contains('الجار')) return '🏠';
    if (theme.contains('الشكر')) return '🙏';
    return '📚';
  }
}
