# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\def\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\ttt\\ttt1" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\def\\flutter"
  "PROJECT_DIR=D:\\ttt\\ttt1"
  "FLUTTER_ROOT=D:\\def\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\ttt\\ttt1\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\ttt\\ttt1"
  "FLUTTER_TARGET=D:\\ttt\\ttt1\\seerah_app\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\ttt\\ttt1\\.dart_tool\\package_config.json"
)
