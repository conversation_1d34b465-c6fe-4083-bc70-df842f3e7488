import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/theme_provider.dart' as theme_provider;

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الإعدادات',
          style: TextStyle(
            fontFamily: 'Amiri',
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Consumer<theme_provider.ThemeProvider>(
        builder: (context, themeProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildThemeSection(context, themeProvider),
              const SizedBox(height: 24),
              _buildFontSection(context, themeProvider),
              const SizedBox(height: 24),
              _buildAboutSection(context),
              const SizedBox(height: 24),
              _buildContactSection(context),
              const SizedBox(height: 24),
              _buildResetSection(context, themeProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildThemeSection(BuildContext context, theme_provider.ThemeProvider themeProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المظهر',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Theme mode selection
            Text(
              'وضع المظهر',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<theme_provider.ThemeMode>(
                    title: const Text('فاتح', style: TextStyle(fontFamily: 'Amiri')),
                    value: theme_provider.ThemeMode.light,
                    groupValue: themeProvider.themeMode,
                    onChanged: (value) => themeProvider.setThemeMode(value!),
                    dense: true,
                  ),
                ),
                Expanded(
                  child: RadioListTile<theme_provider.ThemeMode>(
                    title: const Text('داكن', style: TextStyle(fontFamily: 'Amiri')),
                    value: theme_provider.ThemeMode.dark,
                    groupValue: themeProvider.themeMode,
                    onChanged: (value) => themeProvider.setThemeMode(value!),
                    dense: true,
                  ),
                ),
              ],
            ),
            RadioListTile<theme_provider.ThemeMode>(
              title: const Text('تلقائي (حسب النظام)', style: TextStyle(fontFamily: 'Amiri')),
              value: theme_provider.ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (value) => themeProvider.setThemeMode(value!),
              dense: true,
            ),

            const SizedBox(height: 16),

            // App theme selection
            Text(
              'لون التطبيق',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontFamily: 'Amiri',
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: theme_provider.AppTheme.values.map((theme) {
                return ChoiceChip(
                  label: Text(
                    _getThemeName(theme),
                    style: const TextStyle(fontFamily: 'Amiri'),
                  ),
                  selected: themeProvider.appTheme == theme,
                  onSelected: (selected) {
                    if (selected) {
                      themeProvider.setAppTheme(theme);
                    }
                  },
                  avatar: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: _getThemeColor(theme),
                      shape: BoxShape.circle,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSection(BuildContext context, theme_provider.ThemeProvider themeProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الخط',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Font size
            Text(
              'حجم الخط: ${themeProvider.fontSize.toInt()}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontFamily: 'Amiri',
              ),
            ),
            Slider(
              value: themeProvider.fontSize,
              min: 12.0,
              max: 24.0,
              divisions: 6,
              onChanged: (value) => themeProvider.setFontSize(value),
            ),

            const SizedBox(height: 16),

            // Font family
            SwitchListTile(
              title: const Text(
                'استخدام خط النظام',
                style: TextStyle(fontFamily: 'Amiri'),
              ),
              subtitle: const Text(
                'استخدام الخط الافتراضي للنظام بدلاً من خط عمري',
                style: TextStyle(fontFamily: 'Amiri'),
              ),
              value: themeProvider.useSystemFont,
              onChanged: (value) => themeProvider.setUseSystemFont(value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حول التطبيق',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showAboutDialog(context),
                icon: const Icon(Icons.info_outline),
                label: const Text(
                  'معلومات التطبيق',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResetSection(BuildContext context, theme_provider.ThemeProvider themeProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إعادة تعيين',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showResetDialog(context, themeProvider),
                icon: const Icon(Icons.restore),
                label: const Text(
                  'إعادة تعيين جميع الإعدادات',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showResetDialog(BuildContext context, theme_provider.ThemeProvider themeProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'إعادة تعيين الإعدادات',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        content: const Text(
          'هل أنت متأكد من إعادة تعيين جميع إعدادات المظهر إلى القيم الافتراضية؟',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              themeProvider.resetToDefaults();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'تم إعادة تعيين الإعدادات بنجاح',
                    style: TextStyle(fontFamily: 'Amiri'),
                  ),
                ),
              );
            },
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogTheme.backgroundColor ?? Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.mosque,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'حول التطبيق',
              style: TextStyle(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      '🕌',
                      style: TextStyle(
                        fontSize: 48,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'السيرة النبوية الشريفة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Amiri',
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'الإصدار 1.0.0',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                        fontFamily: 'Amiri',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'تطبيق شامل لتعلم السيرة النبوية الشريفة والأحاديث النبوية وسير الصحابة الكرام رضوان الله عليهم.',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                  fontFamily: 'Amiri',
                  height: 1.5,
                ),
                textAlign: TextAlign.justify,
              ),
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.developer_mode,
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          'المطور',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Amiri',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'شايبي وائل',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Amiri',
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'جميع الحقوق محفوظة © 2025',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                        fontFamily: 'Amiri',
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: TextStyle(
                fontFamily: 'Amiri',
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التواصل مع المطور',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showContactDialog(context),
                icon: const Icon(Icons.email_outlined),
                label: const Text(
                  'إرسال رسالة',
                  style: TextStyle(fontFamily: 'Amiri'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showContactDialog(BuildContext context) {
    final TextEditingController subjectController = TextEditingController();
    final TextEditingController messageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogTheme.backgroundColor ?? Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.email,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'التواصل مع المطور',
              style: TextStyle(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'سيتم إرسال رسالتك إلى البريد الإلكتروني للمطور',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                          fontFamily: 'Amiri',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'موضوع الرسالة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Amiri',
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: subjectController,
                decoration: InputDecoration(
                  hintText: 'اكتب موضوع الرسالة...',
                  hintStyle: const TextStyle(fontFamily: 'Amiri'),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.subject),
                ),
                style: const TextStyle(fontFamily: 'Amiri'),
              ),
              const SizedBox(height: 16),
              Text(
                'نص الرسالة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Amiri',
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: messageController,
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'اكتب رسالتك هنا...',
                  hintStyle: const TextStyle(fontFamily: 'Amiri'),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.message),
                ),
                style: const TextStyle(fontFamily: 'Amiri'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontFamily: 'Amiri',
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              if (subjectController.text.trim().isNotEmpty &&
                  messageController.text.trim().isNotEmpty) {
                _sendEmail(context, subjectController.text, messageController.text);
                Navigator.pop(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                      'يرجى ملء جميع الحقول',
                      style: TextStyle(fontFamily: 'Amiri'),
                    ),
                    backgroundColor: Theme.of(context).colorScheme.error,
                  ),
                );
              }
            },
            icon: const Icon(Icons.send),
            label: const Text(
              'إرسال',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  void _sendEmail(BuildContext context, String subject, String message) async {
    try {
      // إنشاء رابط mailto
      final String emailAddress = '<EMAIL>';
      final String encodedSubject = Uri.encodeComponent(subject);
      final String encodedBody = Uri.encodeComponent(
        'الموضوع: $subject\n\n'
        'الرسالة:\n$message\n\n'
        '---\n'
        'تم الإرسال من تطبيق السيرة النبوية الشريفة\n'
        'المطور: شايبي وائل 2025'
      );

      final Uri mailtoUri = Uri.parse('mailto:$emailAddress?subject=$encodedSubject&body=$encodedBody');

      // محاولة فتح تطبيق البريد الإلكتروني مع إعدادات محسنة لأندرويد 12+
      bool emailLaunched = false;

      try {
        // المحاولة الأولى: استخدام mailto مع LaunchMode.externalApplication
        if (await canLaunchUrl(mailtoUri)) {
          await launchUrl(
            mailtoUri,
            mode: LaunchMode.externalApplication,
          );
          emailLaunched = true;
        }
      } catch (e) {
        debugPrint('فشل في المحاولة الأولى: $e');
      }

      // المحاولة الثانية: استخدام intent مباشر للأندرويد
      if (!emailLaunched) {
        try {
          final Uri androidEmailUri = Uri.parse('intent://send?to=$emailAddress&subject=$encodedSubject&body=$encodedBody#Intent;scheme=mailto;package=com.google.android.gm;end');
          if (await canLaunchUrl(androidEmailUri)) {
            await launchUrl(androidEmailUri);
            emailLaunched = true;
          }
        } catch (e) {
          debugPrint('فشل في المحاولة الثانية: $e');
        }
      }

      if (emailLaunched) {
        // عرض رسالة نجاح
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'تم فتح تطبيق البريد الإلكتروني بنجاح! ✅',
                style: TextStyle(fontFamily: 'Amiri'),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
              action: SnackBarAction(
                label: 'موافق',
                textColor: Colors.white,
                onPressed: () {},
              ),
            ),
          );
        }
      } else {
        // في حالة عدم توفر تطبيق بريد إلكتروني
        if (context.mounted) {
          _showEmailNotAvailableDialog(context, subject, message);
        }
      }
    } catch (e) {
      // في حالة حدوث خطأ
      if (context.mounted) {
        _showEmailNotAvailableDialog(context, subject, message);
      }
    }
  }

  void _showEmailNotAvailableDialog(BuildContext context, String subject, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'تطبيق البريد الإلكتروني غير متوفر',
          style: TextStyle(fontFamily: 'Amiri'),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'يرجى إرسال رسالتك يدوياً إلى:',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                '<EMAIL>',
                style: TextStyle(
                  fontFamily: 'Amiri',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'محتوى الرسالة:',
              style: TextStyle(
                fontFamily: 'Amiri',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                'الموضوع: $subject\n\nالرسالة:\n$message',
                style: const TextStyle(fontFamily: 'Amiri'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'موافق',
              style: TextStyle(fontFamily: 'Amiri'),
            ),
          ),
        ],
      ),
    );
  }

  String _getThemeName(theme_provider.AppTheme theme) {
    switch (theme) {
      case theme_provider.AppTheme.green:
        return 'أخضر';
      case theme_provider.AppTheme.blue:
        return 'أزرق';
      case theme_provider.AppTheme.purple:
        return 'بنفسجي';
      case theme_provider.AppTheme.orange:
        return 'برتقالي';
      case theme_provider.AppTheme.teal:
        return 'أزرق مخضر';
      case theme_provider.AppTheme.brown:
        return 'بني';
    }
  }

  Color _getThemeColor(theme_provider.AppTheme theme) {
    switch (theme) {
      case theme_provider.AppTheme.green:
        return const Color(0xFF4CAF50);
      case theme_provider.AppTheme.blue:
        return const Color(0xFF2196F3);
      case theme_provider.AppTheme.purple:
        return const Color(0xFF9C27B0);
      case theme_provider.AppTheme.orange:
        return const Color(0xFFFF9800);
      case theme_provider.AppTheme.teal:
        return const Color(0xFF009688);
      case theme_provider.AppTheme.brown:
        return const Color(0xFF795548);
    }
  }
}