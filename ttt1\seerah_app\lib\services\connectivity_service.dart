import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  static final Connectivity _connectivity = Connectivity();
  static StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  static bool _isConnected = false;
  static final List<Function(bool)> _listeners = [];

  /// تهيئة خدمة الاتصال
  static Future<void> initialize() async {
    // فحص الحالة الأولية
    await _checkInitialConnectivity();

    // الاستماع للتغييرات
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        debugPrint('Connectivity error: $error');
      },
    );
  }

  /// فحص الحالة الأولية للاتصال
  static Future<void> _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      debugPrint('Error checking initial connectivity: $e');
      _isConnected = false;
    }
  }

  /// معالج تغيير حالة الاتصال
  static void _onConnectivityChanged(List<ConnectivityResult> results) {
    _updateConnectionStatus(results);
  }

  /// تحديث حالة الاتصال
  static void _updateConnectionStatus(List<ConnectivityResult> results) {
    final wasConnected = _isConnected;

    // فحص إذا كان هناك أي اتصال متاح
    _isConnected = results.any((result) =>
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.ethernet
    );

    debugPrint('🌐 Connectivity changed: $_isConnected (${results.join(', ')})');

    // إشعار المستمعين إذا تغيرت الحالة
    if (wasConnected != _isConnected) {
      _notifyListeners(_isConnected);
    }
  }

  /// إشعار جميع المستمعين
  static void _notifyListeners(bool isConnected) {
    for (final listener in _listeners) {
      try {
        listener(isConnected);
      } catch (e) {
        debugPrint('Error notifying connectivity listener: $e');
      }
    }
  }

  /// إضافة مستمع لتغييرات الاتصال
  static void addListener(Function(bool) listener) {
    if (!_listeners.contains(listener)) {
      _listeners.add(listener);
    }
  }

  /// إزالة مستمع
  static void removeListener(Function(bool) listener) {
    _listeners.remove(listener);
  }

  /// فحص إذا كان هناك اتصال بالإنترنت
  static bool get isConnected => _isConnected;

  /// فحص متقدم للاتصال (مع ping)
  static Future<bool> hasInternetConnection() async {
    if (!_isConnected) {
      return false;
    }

    try {
      // محاولة الاتصال بـ Google DNS
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      debugPrint('Internet connection test failed: $e');
      return false;
    }
  }

  /// الحصول على نوع الاتصال الحالي
  static Future<String> getConnectionType() async {
    try {
      final result = await _connectivity.checkConnectivity();

      if (result.contains(ConnectivityResult.wifi)) {
        return 'WiFi';
      } else if (result.contains(ConnectivityResult.mobile)) {
        return 'Mobile Data';
      } else if (result.contains(ConnectivityResult.ethernet)) {
        return 'Ethernet';
      } else {
        return 'No Connection';
      }
    } catch (e) {
      debugPrint('Error getting connection type: $e');
      return 'Unknown';
    }
  }

  /// انتظار الاتصال بالإنترنت
  static Future<void> waitForConnection({Duration? timeout}) async {
    if (_isConnected) {
      return;
    }

    final completer = Completer<void>();
    late Function(bool) listener;

    listener = (bool isConnected) {
      if (isConnected) {
        removeListener(listener);
        if (!completer.isCompleted) {
          completer.complete();
        }
      }
    };

    addListener(listener);

    // إضافة timeout إذا تم تحديده
    if (timeout != null) {
      Timer(timeout, () {
        removeListener(listener);
        if (!completer.isCompleted) {
          completer.completeError(TimeoutException('Connection timeout', timeout));
        }
      });
    }

    return completer.future;
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    await _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
    _listeners.clear();
  }

  /// إعادة تهيئة الخدمة
  static Future<void> reset() async {
    await dispose();
    await initialize();
  }

  /// فحص دوري للاتصال
  static Timer startPeriodicCheck({Duration interval = const Duration(minutes: 1)}) {
    return Timer.periodic(interval, (timer) async {
      await _checkInitialConnectivity();
    });
  }
}

/// استثناء انتهاء المهلة الزمنية
class TimeoutException implements Exception {
  final String message;
  final Duration? duration;

  const TimeoutException(this.message, [this.duration]);

  @override
  String toString() {
    if (duration != null) {
      return 'TimeoutException: $message (${duration!.inSeconds}s)';
    }
    return 'TimeoutException: $message';
  }
}
