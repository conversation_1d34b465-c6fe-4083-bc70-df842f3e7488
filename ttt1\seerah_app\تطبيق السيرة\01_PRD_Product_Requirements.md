# 📋 **PRD - وثيقة متطلبات المنتج**
## **تطبيق السيرة النبوية الشريفة**

---

## 🎯 **نظرة عامة على المنتج**

### **اسم التطبيق**
**"سيرة النبي محمد ﷺ"** - تطبيق شامل لتعلم السيرة النبوية الشريفة

### **الهدف الأساسي**
تطبيق تعليمي إسلامي شامل يهدف إلى نشر تعاليم الإسلام وسيرة النبي محمد ﷺ من خلال واجهة عربية جميلة وسهلة الاستخدام.

### **الجمهور المستهدف**
- المسلمون من جميع الأعمار الراغبون في تعلم السيرة النبوية
- طلاب العلوم الشرعية والدراسات الإسلامية
- المهتمون بالتاريخ الإسلامي والأحاديث النبوية
- الباحثون في سير الصحابة الكرام

### **المنصات المدعومة**
- **Flutter (الأصلي)**: iOS, Android, Web, Desktop
- **Android Native**: Kotlin - إصدارات Android 9.0+ (API 28+)

---

## 📚 **المحتوى والبيانات**

### **1. السيرة النبوية (30 حدث)**

#### **التصنيفات:**
- **الطفولة والشباب** (10 أحداث)
- **البعثة والدعوة** (8 أحداث)
- **الهجرة والمدينة** (7 أحداث)
- **الغزوات والفتوحات** (5 أحداث)

#### **هيكل البيانات:**
```
SeerahEvent {
  id: String (فريد)
  title: String (عنوان الحدث)
  description: String (وصف مختصر)
  detailedDescription: String (وصف مفصل)
  category: String (التصنيف)
  period: String (فترة مكة/المدينة)
  ageOfProphet: String (عمر النبي)
  location: String (المكان)
  islamicYear: String (السنة الهجرية)
  christianYear: String (السنة الميلادية)
  significance: String (الأهمية التاريخية)
  lessons: String (الدروس المستفادة)
  relatedVerses: String (الآيات ذات الصلة)
  relatedHadiths: String (الأحاديث ذات الصلة)
  order: Int (ترتيب الحدث)
  importance: Int (1-5 نجوم)
  emoji: String (رمز تعبيري)
}
```

#### **الأحداث الأساسية:**
1. **ولادة النبي محمد ﷺ** (571م - عام الفيل)
2. **وفاة والده عبد الله** (قبل الولادة)
3. **وفاة أمه آمنة بنت وهب** (577م - عمر 6 سنوات)
4. **كفالة جده عبد المطلب** (577م)
5. **كفالة عمه أبو طالب** (579م)
6. **رحلة الشام مع عمه** (582م - عمر 12 سنة)
7. **حرب الفجار** (590م - عمر 20 سنة)
8. **حلف الفضول** (590م)
9. **زواجه من خديجة رضي الله عنها** (595م - عمر 25 سنة)
10. **بناء الكعبة ووضع الحجر الأسود** (605م - عمر 35 سنة)

### **2. الأحاديث النبوية (50 حديث)**

#### **التصنيفات:**
- **أحاديث العقيدة** (10 أحاديث)
- **أحاديث العبادة** (12 حديث)
- **أحاديث الأخلاق** (12 حديث)
- **أحاديث المعاملات** (8 أحاديث)
- **أحاديث الآداب الاجتماعية** (4 أحاديث)
- **أحاديث الدعاء والذكر** (4 أحاديث)

#### **هيكل البيانات:**
```
Hadith {
  id: String (فريد)
  arabicText: String (النص العربي الأصلي)
  translation: String (الترجمة)
  explanation: String (الشرح المفصل)
  realLifeExample: String (مثال من الحياة الواقعية)
  narrator: String (الراوي)
  source: String (المصدر - صحيح البخاري، مسلم، إلخ)
  category: String (التصنيف)
  theme: String (الموضوع الرئيسي)
  number: Int (رقم الحديث)
  keywords: List<String> (كلمات مفتاحية للبحث)
  isAuthentic: Boolean (صحة الحديث)
  isBookmarked: Boolean (مضاف للمفضلة)
}
```

#### **أمثلة الأحاديث الأساسية:**
1. **حديث النية**: "إنما الأعمال بالنيات..."
2. **حديث جبريل**: "أن تعبد الله كأنك تراه..."
3. **حديث الإسلام على خمس**: "بني الإسلام على خمس..."
4. **حديث الرحمة**: "الراحمون يرحمهم الرحمن..."
5. **حديث حسن الخلق**: "إنما بعثت لأتمم مكارم الأخلاق"

### **3. الصحابة الكرام (25 صحابي)**

#### **التصنيفات:**
- **الخلفاء الراشدون** (4 صحابة)
- **العشرة المبشرون بالجنة** (6 صحابة)
- **أمهات المؤمنين** (5 زوجات)
- **الأنصار** (5 صحابة)
- **المهاجرون** (3 صحابة)
- **الصحابيات** (2 صحابيات)

#### **هيكل البيانات:**
```
Companion {
  id: String (فريد)
  name: String (الاسم)
  fullName: String (الاسم الكامل)
  nickname: String (الكنية واللقب)
  biography: String (السيرة المختصرة)
  birthYear: String (سنة الولادة)
  deathYear: String (سنة الوفاة)
  birthPlace: String (مكان الولادة)
  deathPlace: String (مكان الوفاة)
  tribe: String (القبيلة)
  category: String (التصنيف)
  famousQualities: String (الصفات المشهورة)
  majorContributions: String (الإنجازات الرئيسية)
  relationWithProphet: String (العلاقة مع النبي)
  famousHadiths: String (الأحاديث المشهورة)
  conversionStory: String (قصة الإسلام)
  afterProphetDeath: String (بعد وفاة النبي)
  lessons: String (الدروس المستفادة)
  achievements: List<String> (قائمة الإنجازات)
  virtues: List<String> (قائمة الفضائل)
  keywords: List<String> (كلمات مفتاحية للبحث)
  number: Int (رقم الصحابي)
}
```

---

## 🖥️ **الواجهات والشاشات**

### **1. الشاشة الرئيسية (Home Screen)**

#### **المكونات:**
- **AppBar**: عنوان التطبيق + زر القائمة + زر الإشعارات
- **أيقونة التطبيق**: 🕌 مع خلفية شفافة
- **العنوان الرئيسي**: "سيرة النبي محمد ﷺ"
- **كروت الأقسام الثلاثة**:
  - كارت السيرة النبوية (أخضر #4CAF50)
  - كارت الأحاديث النبوية (برتقالي #FF9800)
  - كارت الصحابة الكرام (بنفسجي #9C27B0)

#### **العدادات المتحركة:**
- عدد أحداث السيرة: 30 حدث
- عدد الأحاديث: 50 حديث
- عدد الصحابة: 25 صحابي

#### **السلوك:**
- النقر على أي كارت ينقل للقسم المطلوب
- العدادات تتحرك من 0 إلى الرقم النهائي عند فتح الشاشة

### **2. شاشة السيرة النبوية (Seerah Screen)**

#### **المكونات:**
- **Header**: زر الرجوع + العنوان "السيرة النبوية"
- **قائمة الأحداث**: عرض جميع الـ30 حدث في بطاقات
- **كارت الإحصائيات**: إجمالي الأحداث والفئات

#### **تصميم البطاقة:**
```
[🎯 رمز الحدث] عنوان الحدث
📅 السنة - 📍 المكان
📂 الفئة | ⭐ الأهمية: X/5
وصف مختصر للحدث...
[⭐ أيقونة المفضلة]
```

#### **السلوك:**
- النقر على البطاقة يفتح تفاصيل الحدث
- النقر على ⭐ يضيف/يزيل من المفضلة
- ترتيب زمني من الأقدم للأحدث

### **3. شاشة الأحاديث النبوية (Hadith Screen)**

#### **المكونات:**
- **Header**: زر الرجوع + العنوان "الأحاديث النبوية"
- **قائمة الأحاديث**: عرض جميع الـ50 حديث
- **كارت الإحصائيات**: إجمالي الأحاديث والفئات

#### **تصميم البطاقة:**
```
💬 موضوع الحديث
"بداية النص العربي..."
📚 المصدر | 👤 الراوي
📂 الفئة
[⭐ أيقونة المفضلة]
```

#### **السلوك:**
- النقر على البطاقة يفتح النص الكامل والشرح
- النقر على ⭐ يضيف/يزيل من المفضلة
- ترتيب حسب الأهمية والموضوع

### **4. شاشة الصحابة الكرام (Companions Screen)**

#### **المكونات:**
- **Header**: زر الرجوع + العنوان "الصحابة الكرام"
- **قائمة الصحابة**: عرض جميع الـ25 صحابي
- **كارت الإحصائيات**: إجمالي الصحابة والفئات

#### **تصميم البطاقة:**
```
👑 اسم الصحابي
🏷️ الكنية واللقب
🏠 مكان الولادة | 👥 القبيلة
وصف مختصر للسيرة...
⭐ مشهور بـ: الصفات الرئيسية
[⭐ أيقونة المفضلة]
```

### **5. شاشة البحث (Search Screen)**

#### **المكونات:**
- **مربع البحث**: "ابحث في السيرة والأحاديث والصحابة..."
- **الكلمات المفتاحية الشائعة**: عرض 8 كلمات شائعة
- **إحصائيات البحث**: عدد العناصر في كل قسم
- **نصائح البحث**: إرشادات للحصول على أفضل النتائج

#### **السلوك:**
- البحث في الوقت الفعلي أثناء الكتابة
- البحث في جميع الأقسام (السيرة، الأحاديث، الصحابة)
- إبراز النتائج المطابقة

### **6. شاشة المفضلة (Favorites Screen)**

#### **الحالة الفارغة:**
- أيقونة 📋 كبيرة
- رسالة "لا توجد مفضلة حالياً"
- إرشادات لإضافة المفضلة
- أزرار سريعة للانتقال للأقسام

#### **الحالة مع المحتوى:**
- إحصائيات المفضلة حسب النوع
- قائمة العناصر المفضلة
- إمكانية إزالة من المفضلة

---

## 🎨 **التصميم والألوان**

### **نظام الألوان الأساسي:**
- **الخلفية المتدرجة**:
  - أخضر داكن جداً: #102c23
  - أخضر داكن: #183f33
  - أخضر متوسط: #123127
  - أخضر فاتح نسبياً: #113026

### **ألوان الأقسام:**
- **السيرة النبوية**: أخضر #4CAF50
- **الأحاديث النبوية**: برتقالي #FF9800
- **الصحابة الكرام**: بنفسجي #9C27B0

### **ألوان النصوص:**
- **النص الأساسي**: أبيض #FFFFFF
- **النص الثانوي**: أبيض شفاف #CCFFFFFF
- **النص على الخلفية البيضاء**: أسود #333333
- **النص الثانوي على البيضاء**: رمادي #666666

### **التصميم العام:**
- **الخطوط**: دعم كامل للعربية مع RTL
- **الأشكال**: زوايا مدورة 12-16px
- **الظلال**: ظل خفيف للبطاقات
- **الرسوم المتحركة**: انتقالات سلسة 300ms

---

## 🔧 **الوظائف الأساسية**

### **1. التنقل (Navigation)**

#### **Bottom Navigation (6 تبويبات):**
1. **الرئيسية** 🏠
2. **السيرة** 📖
3. **الأحاديث** 💬
4. **الصحابة** 👥
5. **البحث** 🔍
6. **المفضلة** ⭐

#### **Drawer Navigation (القائمة الجانبية):**
- **المكافآت والإنجازات** 🏆
- **الإعدادات** ⚙️
- **حول التطبيق** ℹ️
- **تواصل مع المطور** 📧
- **تبديل الثيم** 🌙
- **التباين العالي** 🔍

### **2. نظام المفضلة**

#### **الوظائف:**
- إضافة/إزالة أي عنصر للمفضلة
- حفظ المفضلة محلياً
- عرض إحصائيات المفضلة
- تصنيف المفضلة حسب النوع

#### **التخزين:**
```
SharedPreferences:
- seerah_favorites: List<String> (معرفات الأحداث)
- hadith_favorites: List<String> (معرفات الأحاديث)
- companion_favorites: List<String> (معرفات الصحابة)
```

### **3. نظام البحث**

#### **الوظائف:**
- البحث في الوقت الفعلي
- البحث في جميع الأقسام
- البحث بالكلمات المفتاحية
- إبراز النتائج المطابقة

#### **خوارزمية البحث:**
- البحث في العناوين (وزن 3)
- البحث في الأوصاف (وزن 2)
- البحث في الكلمات المفتاحية (وزن 1)

### **4. نظام المكافآت**

#### **النقاط والمستويات:**
- **النقاط الأساسية**: 150 نقطة
- **المستوى الحالي**: مبتدئ (المستوى 2)
- **نقاط المستوى التالي**: 100 نقطة لكل مستوى

#### **الإنجازات:**
- ✅ **قارئ مبتدئ**: فتح التطبيق
- ✅ **مستكشف**: تصفح الأقسام
- 🔒 **عالم السيرة**: قراءة 20 حدث
- 🔒 **حافظ الأحاديث**: قراءة 30 حديث

---

## 📱 **المتطلبات التقنية**

### **Flutter (الأصلي):**
- **Flutter SDK**: 3.0+
- **Dart**: 3.0+
- **المنصات**: iOS, Android, Web, Desktop

### **Android Native:**
- **Kotlin**: 1.8+
- **Android API**: 28+ (Android 9.0+)
- **Gradle**: 8.0+

### **التبعيات الأساسية:**
- **Provider**: إدارة الحالة
- **SharedPreferences**: التخزين المحلي
- **Material Design 3**: نظام التصميم

---

## 🎯 **معايير النجاح**

### **الوظائف الأساسية:**
- ✅ عرض جميع المحتوى (30 حدث، 50 حديث، 25 صحابي)
- ✅ التنقل السلس بين الشاشات
- ✅ نظام المفضلة يعمل بشكل صحيح
- ✅ البحث يعطي نتائج دقيقة
- ✅ القائمة الجانبية تعمل بالكامل

### **الأداء:**
- ✅ تحميل سريع للشاشات (< 1 ثانية)
- ✅ انتقالات سلسة بدون تأخير
- ✅ استهلاك ذاكرة معقول (< 100MB)

### **تجربة المستخدم:**
- ✅ واجهة عربية جميلة ومتجاوبة
- ✅ دعم كامل للـ RTL
- ✅ ألوان متناسقة وجذابة
- ✅ رسائل واضحة ومفيدة

---

## 📞 **معلومات المطور**

- **المطور**: وائل شايبي
- **البريد الإلكتروني**: <EMAIL>
- **العام**: 2025
- **الإصدار**: 1.0.0

---

## 🔄 **التحديثات المستقبلية**

### **المرحلة الثانية:**
- إضافة الصوتيات للأحاديث
- نظام الإشعارات اليومية
- مشاركة المحتوى
- وضع ليلي محسن

### **المرحلة الثالثة:**
- قاعدة بيانات أكبر
- ميزات تفاعلية متقدمة
- دعم لغات إضافية
- مزامنة سحابية

---

**📋 هذه الوثيقة تحتوي على جميع متطلبات المنتج بالتفصيل الدقيق لضمان إعادة البناء بنسبة 100% مطابقة للأصل.**
