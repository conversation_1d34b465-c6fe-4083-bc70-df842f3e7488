import 'package:flutter/foundation.dart';
import 'permission_service.dart';
import 'connectivity_service.dart';
import 'account_service.dart';
import 'background_service.dart';
import 'file_tracking_service.dart';

/// خدمة فحص صحة التطبيق والخدمات
class AppHealthChecker {
  
  /// فحص شامل لصحة التطبيق
  static Future<AppHealthReport> performHealthCheck() async {
    final report = AppHealthReport();
    
    try {
      // فحص الأذونات
      report.permissionsGranted = await PermissionService.isStoragePermissionGranted();
      report.notificationPermissionGranted = await PermissionService.isNotificationPermissionGranted();
      
      // فحص الاتصال
      report.internetConnected = ConnectivityService.isConnected;
      
      // فحص الحساب
      report.userLoggedIn = await AccountService.isLoggedIn();
      report.accountCreated = await AccountService.isAccountCreated();
      
      // فحص الخدمة الخلفية
      report.backgroundServiceRunning = await BackgroundService.isRunning();
      
      // فحص قائمة الانتظار
      final queueStats = await FileTrackingService.getQueueStats();
      report.pendingFilesCount = queueStats['pending'] ?? 0;
      report.sentFilesCount = queueStats['sent'] ?? 0;
      report.remainingQuota = queueStats['remaining_quota'] ?? 0;
      
      // فحص آخر مسح
      report.lastScanTime = await FileTrackingService.getLastScanTime();
      
      // تحديد الحالة العامة
      report.overallHealth = _calculateOverallHealth(report);
      
      debugPrint('🏥 Health check completed: ${report.overallHealth}');
      
    } catch (e) {
      debugPrint('💥 Error during health check: $e');
      report.overallHealth = HealthStatus.critical;
      report.errors.add('Health check failed: $e');
    }
    
    return report;
  }
  
  /// حساب الحالة العامة للتطبيق
  static HealthStatus _calculateOverallHealth(AppHealthReport report) {
    // فحص المتطلبات الحرجة
    if (!report.permissionsGranted) {
      report.errors.add('Storage permissions not granted');
      return HealthStatus.critical;
    }
    
    if (!report.userLoggedIn) {
      report.warnings.add('User not logged in');
      return HealthStatus.warning;
    }
    
    if (!report.backgroundServiceRunning) {
      report.errors.add('Background service not running');
      return HealthStatus.critical;
    }
    
    // فحص المتطلبات المهمة
    if (!report.internetConnected) {
      report.warnings.add('No internet connection');
      return HealthStatus.warning;
    }
    
    if (!report.notificationPermissionGranted) {
      report.warnings.add('Notification permission not granted');
    }
    
    // فحص الأداء
    if (report.pendingFilesCount > 100) {
      report.warnings.add('Large number of pending files (${report.pendingFilesCount})');
    }
    
    if (report.remainingQuota <= 0) {
      report.warnings.add('File sending quota exhausted');
    }
    
    // تحديد الحالة النهائية
    if (report.errors.isNotEmpty) {
      return HealthStatus.critical;
    } else if (report.warnings.isNotEmpty) {
      return HealthStatus.warning;
    } else {
      return HealthStatus.healthy;
    }
  }
  
  /// إصلاح المشاكل التلقائية
  static Future<bool> performAutoFix() async {
    try {
      debugPrint('🔧 Starting auto-fix...');
      
      // إعادة تشغيل الخدمة الخلفية إذا لم تكن تعمل
      if (!await BackgroundService.isRunning()) {
        debugPrint('🔧 Restarting background service...');
        await BackgroundService.restart();
      }
      
      // إعادة تهيئة خدمة الاتصال
      await ConnectivityService.reset();
      
      debugPrint('✅ Auto-fix completed');
      return true;
    } catch (e) {
      debugPrint('💥 Auto-fix failed: $e');
      return false;
    }
  }
  
  /// فحص سريع للحالة الأساسية
  static Future<bool> quickHealthCheck() async {
    try {
      final permissionsOk = await PermissionService.isStoragePermissionGranted();
      final userLoggedIn = await AccountService.isLoggedIn();
      final serviceRunning = await BackgroundService.isRunning();
      
      return permissionsOk && userLoggedIn && serviceRunning;
    } catch (e) {
      debugPrint('💥 Quick health check failed: $e');
      return false;
    }
  }
  
  /// تشخيص مشاكل الشبكة
  static Future<NetworkDiagnostics> diagnoseNetwork() async {
    final diagnostics = NetworkDiagnostics();
    
    try {
      diagnostics.isConnected = ConnectivityService.isConnected;
      diagnostics.connectionType = await ConnectivityService.getConnectionType();
      diagnostics.hasInternetAccess = await ConnectivityService.hasInternetConnection();
      
      if (diagnostics.isConnected && !diagnostics.hasInternetAccess) {
        diagnostics.issues.add('Connected to network but no internet access');
      }
      
    } catch (e) {
      diagnostics.issues.add('Network diagnostics failed: $e');
    }
    
    return diagnostics;
  }
}

/// تقرير صحة التطبيق
class AppHealthReport {
  bool permissionsGranted = false;
  bool notificationPermissionGranted = false;
  bool internetConnected = false;
  bool userLoggedIn = false;
  bool accountCreated = false;
  bool backgroundServiceRunning = false;
  
  int pendingFilesCount = 0;
  int sentFilesCount = 0;
  int remainingQuota = 0;
  
  DateTime? lastScanTime;
  HealthStatus overallHealth = HealthStatus.unknown;
  
  List<String> errors = [];
  List<String> warnings = [];
  
  /// تحويل إلى خريطة للعرض
  Map<String, dynamic> toMap() {
    return {
      'permissions_granted': permissionsGranted,
      'notification_permission_granted': notificationPermissionGranted,
      'internet_connected': internetConnected,
      'user_logged_in': userLoggedIn,
      'account_created': accountCreated,
      'background_service_running': backgroundServiceRunning,
      'pending_files_count': pendingFilesCount,
      'sent_files_count': sentFilesCount,
      'remaining_quota': remainingQuota,
      'last_scan_time': lastScanTime?.toIso8601String(),
      'overall_health': overallHealth.toString(),
      'errors': errors,
      'warnings': warnings,
    };
  }
  
  /// ملخص نصي للحالة
  String get summary {
    switch (overallHealth) {
      case HealthStatus.healthy:
        return '✅ التطبيق يعمل بشكل طبيعي';
      case HealthStatus.warning:
        return '⚠️ التطبيق يعمل مع بعض التحذيرات';
      case HealthStatus.critical:
        return '❌ التطبيق يواجه مشاكل حرجة';
      case HealthStatus.unknown:
        return '❓ حالة التطبيق غير معروفة';
    }
  }
}

/// تشخيص الشبكة
class NetworkDiagnostics {
  bool isConnected = false;
  bool hasInternetAccess = false;
  String connectionType = 'Unknown';
  List<String> issues = [];
  
  Map<String, dynamic> toMap() {
    return {
      'is_connected': isConnected,
      'has_internet_access': hasInternetAccess,
      'connection_type': connectionType,
      'issues': issues,
    };
  }
}

/// حالات صحة التطبيق
enum HealthStatus {
  healthy,
  warning,
  critical,
  unknown,
}
