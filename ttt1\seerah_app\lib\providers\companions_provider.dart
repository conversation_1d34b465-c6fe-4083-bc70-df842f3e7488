import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/companion.dart';
import '../data/companions_data.dart';

class CompanionsProvider extends ChangeNotifier {
  List<Companion> _companions = [];
  List<Companion> _filteredCompanions = [];
  List<String> _bookmarkedIds = [];
  String _selectedCategory = 'الكل';
  String _searchQuery = '';
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Companion> get companions => _companions;
  List<Companion> get filteredCompanions => _filteredCompanions;
  List<String> get bookmarkedIds => _bookmarkedIds;
  String get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get bookmarked companions
  List<Companion> get bookmarkedCompanions {
    return _companions.where((companion) => _bookmarkedIds.contains(companion.id)).toList();
  }

  // Get companions count by category
  Map<String, int> get companionsStats {
    final stats = <String, int>{};
    final categories = CompanionsData.getCategories();
    
    for (final category in categories) {
      if (category == 'الكل') {
        stats[category] = _companions.length;
      } else {
        stats[category] = _companions.where((c) => c.category == category).length;
      }
    }
    
    return stats;
  }

  // Initialize provider
  Future<void> initialize() async {
    await _loadBookmarks();
    await loadCompanions();
  }

  // Load companions data
  Future<void> loadCompanions() async {
    try {
      _setLoading(true);
      _setError(null);

      // Simulate network delay for better UX
      await Future.delayed(const Duration(milliseconds: 500));

      _companions = CompanionsData.getAllCompanions();
      _updateBookmarkStatus();
      _applyFilters();

      _setLoading(false);
    } catch (e) {
      _setError('فشل في تحميل بيانات الصحابة: ${e.toString()}');
      _setLoading(false);
    }
  }

  // Set category filter
  void setCategory(String category) {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      _applyFilters();
      notifyListeners();
    }
  }

  // Set search query
  void setSearchQuery(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      _applyFilters();
      notifyListeners();
    }
  }

  // Clear search
  void clearSearch() {
    _searchQuery = '';
    _applyFilters();
    notifyListeners();
  }

  // Toggle bookmark
  Future<void> toggleBookmark(String companionId) async {
    try {
      if (_bookmarkedIds.contains(companionId)) {
        _bookmarkedIds.remove(companionId);
      } else {
        _bookmarkedIds.add(companionId);
      }

      _updateBookmarkStatus();
      await _saveBookmarks();
      notifyListeners();
    } catch (e) {
      _setError('فشل في حفظ المفضلة: ${e.toString()}');
    }
  }

  // Check if companion is bookmarked
  bool isBookmarked(String companionId) {
    return _bookmarkedIds.contains(companionId);
  }

  // Get companion by ID
  Companion? getCompanionById(String id) {
    try {
      return _companions.firstWhere((companion) => companion.id == id);
    } catch (e) {
      return null;
    }
  }

  // Search companions
  List<Companion> searchCompanions(String query) {
    if (query.isEmpty) return _companions;
    
    return _companions.where((companion) {
      return companion.name.toLowerCase().contains(query.toLowerCase()) ||
             companion.fullName.toLowerCase().contains(query.toLowerCase()) ||
             companion.nickname.toLowerCase().contains(query.toLowerCase()) ||
             companion.biography.toLowerCase().contains(query.toLowerCase()) ||
             companion.category.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Get companions by category
  List<Companion> getCompanionsByCategory(String category) {
    if (category == 'الكل') return _companions;
    return _companions.where((companion) => companion.category == category).toList();
  }

  // Private methods
  void _applyFilters() {
    List<Companion> filtered = List.from(_companions);

    // Apply category filter
    if (_selectedCategory != 'الكل') {
      filtered = filtered.where((companion) => companion.category == _selectedCategory).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((companion) {
        return companion.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               companion.fullName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               companion.nickname.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               companion.biography.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    _filteredCompanions = filtered;
  }

  void _updateBookmarkStatus() {
    _companions = _companions.map((companion) {
      return companion.copyWith(
        isBookmarked: _bookmarkedIds.contains(companion.id),
      );
    }).toList();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load bookmarks from SharedPreferences
  Future<void> _loadBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bookmarks = prefs.getStringList('bookmarked_companions') ?? [];
      _bookmarkedIds = bookmarks;
    } catch (e) {
      debugPrint('فشل في تحميل المفضلة: $e');
    }
  }

  // Save bookmarks to SharedPreferences
  Future<void> _saveBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('bookmarked_companions', _bookmarkedIds);
    } catch (e) {
      debugPrint('فشل في حفظ المفضلة: $e');
    }
  }

  // Refresh data
  Future<void> refresh() async {
    await loadCompanions();
  }

  // Clear all data
  void clear() {
    _companions.clear();
    _filteredCompanions.clear();
    _selectedCategory = 'الكل';
    _searchQuery = '';
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    clear();
    super.dispose();
  }
}
